/* CSS Reset */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

body {
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
  background-color: var(--color-bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Remove default margins and paddings */
h1, h2, h3, h4, h5, h6,
p, blockquote, pre,
dl, dd, ol, ul,
figure, hr,
fieldset, legend {
  margin: 0;
  padding: 0;
}

/* Remove list styles */
ol, ul {
  list-style: none;
}

/* Remove default table spacing */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* Reset form elements */
button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  margin: 0;
  padding: 0;
  border: none;
  background: none;
  color: inherit;
}

button {
  cursor: pointer;
  background: transparent;
  border: none;
  outline: none;
}

input,
textarea,
select {
  outline: none;
  
  &:focus {
    @include focus-ring;
  }
}

/* Remove default button styles */
button,
[type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: none;
  appearance: none;
}

/* Remove default input styles */
input[type="search"] {
  -webkit-appearance: none;
  appearance: none;
}

/* Links */
a {
  color: inherit;
  text-decoration: none;
  
  &:focus {
    @include focus-ring;
  }
}

/* Images */
img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
  height: auto;
}

img {
  border-style: none;
}

/* Remove default fieldset styles */
fieldset {
  border: none;
  margin: 0;
  padding: 0;
}

/* Remove default legend styles */
legend {
  display: table;
  max-width: 100%;
  white-space: normal;
  color: inherit;
}

/* Remove default progress styles */
progress {
  vertical-align: baseline;
}

/* Remove default textarea resize */
textarea {
  resize: vertical;
}

/* Remove default details styles */
details {
  display: block;
}

summary {
  display: list-item;
  cursor: pointer;
}

/* Hidden attribute */
[hidden] {
  display: none !important;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  :root {
    --color-text-primary: #ffffff;
    --color-text-secondary: #cccccc;
    --color-bg-primary: #000000;
    --color-bg-secondary: #111111;
    --color-border: #ffffff;
  }
}

/* Dark mode (default) */
@media (prefers-color-scheme: dark) {
  :root {
    color-scheme: dark;
  }
}

/* Selection */
::selection {
  background-color: var(--color-primary);
  color: var(--color-text-primary);
}

::-moz-selection {
  background-color: var(--color-primary);
  color: var(--color-text-primary);
}
