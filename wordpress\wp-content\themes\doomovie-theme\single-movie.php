<?php
/**
 * The template for displaying single movie posts
 * 
 * @package DoMovie_Theme
 * @version 1.0.0
 */

get_header();

// Increment view count
if (have_posts()) {
    the_post();
    increment_movie_view_count(get_the_ID());
    rewind_posts();
}
?>

<main id="main" class="site-main single-movie-main">
    
    <?php while (have_posts()) : the_post(); 
        $movie_id = get_the_ID();
        $poster_url = get_movie_poster($movie_id);
        $backdrop_url = get_post_meta($movie_id, 'backdrop_url', true);
        $year = get_movie_year($movie_id);
        $rating = get_movie_rating($movie_id);
        $duration = get_movie_duration($movie_id);
        $imdb_rating = get_movie_imdb_rating($movie_id);
        $video_urls = get_movie_video_urls($movie_id);
        $genres = get_the_terms($movie_id, 'movie_genre');
        $countries = get_the_terms($movie_id, 'movie_country');
    ?>

    <!-- Movie Hero Section -->
    <section class="movie-hero" style="background-image: url('<?php echo $backdrop_url ?: $poster_url; ?>');">
        <div class="movie-hero-overlay">
            <div class="container">
                <div class="movie-hero-content">
                    
                    <!-- Movie Poster -->
                    <div class="movie-poster-large">
                        <img src="<?php echo $poster_url; ?>" alt="<?php the_title(); ?>" class="poster-image">
                        
                        <!-- Quality Badge -->
                        <?php if ($quality = get_post_meta($movie_id, 'quality', true)) : ?>
                            <div class="quality-badge">
                                <img src="<?php echo DOOMOVIE_THEME_URL; ?>/assets/images/icons/hd-badge.png" alt="<?php echo $quality; ?>">
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Movie Information -->
                    <div class="movie-details">
                        
                        <!-- Movie Title -->
                        <h1 class="movie-title"><?php the_title(); ?></h1>

                        <!-- Movie Meta -->
                        <div class="movie-meta-info">
                            
                            <?php if ($year) : ?>
                                <span class="meta-item year">
                                    <span class="meta-label"><?php _e('ปี:', 'doomovie-theme'); ?></span>
                                    <span class="meta-value"><?php echo $year; ?></span>
                                </span>
                            <?php endif; ?>

                            <?php if ($rating) : ?>
                                <span class="meta-item age-rating">
                                    <img src="<?php echo DOOMOVIE_THEME_URL; ?>/assets/images/icons/rate-<?php echo $rating; ?>.png" 
                                         alt="<?php echo $rating; ?>" class="rating-icon">
                                </span>
                            <?php endif; ?>

                            <?php if ($duration) : ?>
                                <span class="meta-item duration">
                                    <span class="meta-label"><?php _e('ระยะเวลา:', 'doomovie-theme'); ?></span>
                                    <span class="meta-value"><?php echo $duration; ?></span>
                                </span>
                            <?php endif; ?>

                            <?php if ($imdb_rating) : ?>
                                <span class="meta-item imdb-rating">
                                    <span class="meta-label">IMDB:</span>
                                    <span class="meta-value"><?php echo $imdb_rating; ?>/10</span>
                                </span>
                            <?php endif; ?>

                        </div>

                        <!-- Movie Genres -->
                        <?php if ($genres && !is_wp_error($genres)) : ?>
                            <div class="movie-genres">
                                <span class="genres-label"><?php _e('ประเภท:', 'doomovie-theme'); ?></span>
                                <?php foreach ($genres as $genre) : ?>
                                    <a href="<?php echo get_term_link($genre); ?>" class="genre-tag">
                                        <?php echo $genre->name; ?>
                                    </a>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>

                        <!-- Movie Synopsis -->
                        <div class="movie-synopsis">
                            <h3><?php _e('เรื่องย่อ', 'doomovie-theme'); ?></h3>
                            <div class="synopsis-content">
                                <?php the_content(); ?>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="movie-actions">
                            
                            <!-- Watch Movie Button -->
                            <?php if (!empty(array_filter($video_urls))) : ?>
                                <button class="btn btn-primary btn-lg watch-movie-btn" data-movie-id="<?php echo $movie_id; ?>">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M8 5v14l11-7z"/>
                                    </svg>
                                    <?php _e('ดูหนัง', 'doomovie-theme'); ?>
                                </button>
                            <?php endif; ?>

                            <!-- Trailer Button -->
                            <?php if ($video_urls['trailer']) : ?>
                                <button class="btn btn-secondary btn-lg trailer-btn" data-trailer-url="<?php echo $video_urls['trailer']; ?>">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M10 16.5l6-4.5-6-4.5v9zM12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/>
                                    </svg>
                                    <?php _e('ตัวอย่าง', 'doomovie-theme'); ?>
                                </button>
                            <?php endif; ?>

                            <!-- Add to Watchlist -->
                            <?php if (is_user_logged_in()) : ?>
                                <button class="btn btn-secondary btn-lg add-to-watchlist" data-movie-id="<?php echo $movie_id; ?>">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                                    </svg>
                                    <?php _e('รายการของฉัน', 'doomovie-theme'); ?>
                                </button>
                            <?php endif; ?>

                            <!-- Share Button -->
                            <button class="btn btn-secondary btn-lg share-movie" 
                                    data-movie-title="<?php the_title_attribute(); ?>"
                                    data-movie-url="<?php the_permalink(); ?>">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92s2.92-1.31 2.92-2.92-1.31-2.92-2.92-2.92z"/>
                                </svg>
                                <?php _e('แชร์', 'doomovie-theme'); ?>
                            </button>

                        </div>

                    </div>

                </div>
            </div>
        </div>
    </section>

    <!-- Video Player Section -->
    <section class="video-player-section" id="video-player-section" style="display: none;">
        <div class="container">
            <div class="video-player-container">
                
                <!-- Video Player Header -->
                <div class="video-player-header">
                    <h2 class="player-title"><?php the_title(); ?></h2>
                    <div class="player-controls">
                        
                        <!-- Audio Track Selector -->
                        <div class="audio-selector">
                            <label><?php _e('เสียง:', 'doomovie-theme'); ?></label>
                            <select id="audio-track-selector">
                                <?php if ($video_urls['thai_1']) : ?>
                                    <option value="thai_1"><?php _e('พากย์ไทย 1', 'doomovie-theme'); ?></option>
                                <?php endif; ?>
                                <?php if ($video_urls['thai_2']) : ?>
                                    <option value="thai_2"><?php _e('พากย์ไทย 2', 'doomovie-theme'); ?></option>
                                <?php endif; ?>
                                <?php if ($video_urls['sub']) : ?>
                                    <option value="sub"><?php _e('ซับไทย', 'doomovie-theme'); ?></option>
                                <?php endif; ?>
                            </select>
                        </div>

                        <!-- Close Player -->
                        <button class="close-player-btn" id="close-player-btn">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                            </svg>
                        </button>

                    </div>
                </div>

                <!-- Video Player -->
                <div class="video-player-wrapper">
                    <div id="movie-player" class="movie-player">
                        <!-- Video player will be loaded here -->
                    </div>
                </div>

                <!-- Report Problem -->
                <div class="video-player-footer">
                    <button class="btn btn-sm report-problem-btn" data-movie-id="<?php echo $movie_id; ?>">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/>
                        </svg>
                        <?php _e('แจ้งปัญหา', 'doomovie-theme'); ?>
                    </button>
                </div>

            </div>
        </div>
    </section>

    <!-- Movie Details Section -->
    <section class="movie-details-section">
        <div class="container">
            <div class="row">
                
                <!-- Main Content -->
                <div class="col-lg-8">
                    
                    <!-- Additional Movie Info -->
                    <div class="movie-info-card">
                        <h3><?php _e('ข้อมูลหนัง', 'doomovie-theme'); ?></h3>
                        
                        <div class="info-grid">
                            
                            <?php if ($countries && !is_wp_error($countries)) : ?>
                                <div class="info-item">
                                    <span class="info-label"><?php _e('ประเทศ:', 'doomovie-theme'); ?></span>
                                    <span class="info-value">
                                        <?php echo implode(', ', wp_list_pluck($countries, 'name')); ?>
                                    </span>
                                </div>
                            <?php endif; ?>

                            <?php if ($director = get_post_meta($movie_id, 'director', true)) : ?>
                                <div class="info-item">
                                    <span class="info-label"><?php _e('ผู้กำกับ:', 'doomovie-theme'); ?></span>
                                    <span class="info-value"><?php echo $director; ?></span>
                                </div>
                            <?php endif; ?>

                            <?php if ($cast = get_post_meta($movie_id, 'cast', true)) : ?>
                                <div class="info-item">
                                    <span class="info-label"><?php _e('นักแสดง:', 'doomovie-theme'); ?></span>
                                    <span class="info-value"><?php echo $cast; ?></span>
                                </div>
                            <?php endif; ?>

                            <?php if ($view_count = get_post_meta($movie_id, 'view_count', true)) : ?>
                                <div class="info-item">
                                    <span class="info-label"><?php _e('จำนวนผู้ชม:', 'doomovie-theme'); ?></span>
                                    <span class="info-value"><?php echo number_format($view_count); ?> <?php _e('ครั้ง', 'doomovie-theme'); ?></span>
                                </div>
                            <?php endif; ?>

                        </div>
                    </div>

                    <!-- Comments Section -->
                    <?php if (comments_open() || get_comments_number()) : ?>
                        <div class="movie-comments">
                            <?php comments_template(); ?>
                        </div>
                    <?php endif; ?>

                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    
                    <!-- Related Movies -->
                    <div class="related-movies-widget">
                        <h3><?php _e('หนังที่เกี่ยวข้อง', 'doomovie-theme'); ?></h3>
                        
                        <?php
                        $related_movies = get_related_movies($movie_id, 6);
                        if ($related_movies->have_posts()) :
                        ?>
                            <div class="related-movies-list">
                                <?php
                                while ($related_movies->have_posts()) :
                                    $related_movies->the_post();
                                    get_template_part('template-parts/content/movie-card-small');
                                endwhile;
                                wp_reset_postdata();
                                ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Sidebar Widgets -->
                    <?php if (is_active_sidebar('sidebar-1')) : ?>
                        <div class="movie-sidebar">
                            <?php dynamic_sidebar('sidebar-1'); ?>
                        </div>
                    <?php endif; ?>

                </div>

            </div>
        </div>
    </section>

    <?php endwhile; ?>

</main>

<!-- Video Player Data -->
<script type="application/json" id="movie-player-data">
{
    "movieId": <?php echo $movie_id; ?>,
    "videoUrls": <?php echo json_encode($video_urls); ?>,
    "title": "<?php echo esc_js(get_the_title()); ?>"
}
</script>

<?php get_footer(); ?>
