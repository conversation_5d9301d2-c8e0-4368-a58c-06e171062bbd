{"name": "doomovie-template", "version": "1.0.0", "description": "EZMovie-inspired movie streaming website template", "main": "src/js/main.js", "scripts": {"dev": "webpack --mode development --watch", "build": "webpack --mode production", "start": "webpack serve --mode development --open", "sass": "sass src/scss/main.scss dist/css/style.css --watch", "sass-build": "sass src/scss/main.scss dist/css/style.css --style compressed", "analyze": "node scripts/analyze-website.js", "download-assets": "node scripts/download-assets.js"}, "keywords": ["movie", "streaming", "template", "wordpress", "responsive"], "author": "Your Name", "license": "MIT", "devDependencies": {"@babel/core": "^7.22.0", "@babel/preset-env": "^7.22.0", "autoprefixer": "^10.4.14", "babel-loader": "^9.1.0", "css-loader": "^6.8.1", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.5.3", "mini-css-extract-plugin": "^2.7.6", "postcss": "^8.4.24", "postcss-loader": "^7.3.3", "sass": "^1.63.6", "sass-loader": "^13.3.2", "style-loader": "^3.3.3", "url-loader": "^4.1.1", "webpack": "^5.88.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}, "dependencies": {"axios": "^1.4.0", "cheerio": "^1.0.0-rc.12", "fs-extra": "^11.1.1", "intersection-observer": "^0.12.2", "plyr": "^3.7.8", "puppeteer": "^20.7.2", "swiper": "^10.0.4"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}