/* DoMovie Template - Standalone CSS */

/* CSS Variables */
:root {
  /* Colors */
  --color-primary: #e50914;
  --color-primary-dark: #b20710;
  --color-primary-light: #f40612;
  
  --color-bg-primary: #000000;
  --color-bg-secondary: #141414;
  --color-bg-tertiary: #1a1a1a;
  --color-bg-card: #2a2a2a;
  --color-bg-overlay: rgba(0, 0, 0, 0.8);
  
  --color-text-primary: #ffffff;
  --color-text-secondary: #b3b3b3;
  --color-text-muted: #8c8c8c;
  --color-text-inverse: #000000;
  
  --color-accent-gold: #ffd700;
  --color-accent-green: #46d369;
  --color-accent-blue: #0071eb;
  --color-accent-orange: #ff6b35;
  
  --color-success: #28a745;
  --color-warning: #ffc107;
  --color-error: #dc3545;
  --color-info: #17a2b8;
  
  --color-border: #333333;
  --color-border-light: #555555;
  --color-border-dark: #1a1a1a;
  
  /* Typography */
  --font-primary: 'Noto Sans Thai', 'Helvetica Neue', Arial, sans-serif;
  --font-secondary: 'Roboto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
  --spacing-4xl: 6rem;
  
  /* Border Radius */
  --border-radius-sm: 0.25rem;
  --border-radius-md: 0.375rem;
  --border-radius-lg: 0.5rem;
  --border-radius-xl: 0.75rem;
  --border-radius-2xl: 1rem;
  --border-radius-full: 9999px;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 300ms ease-in-out;
  --transition-slow: 500ms ease-in-out;
  
  /* Z-index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* Reset */
*, *::before, *::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
}

body {
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
  background-color: var(--color-bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  padding-top: 80px; /* Header height */
  padding-bottom: 70px; /* Mobile nav height */
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-md);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
  margin-bottom: var(--spacing-md);
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--color-primary-light);
}

ul, ol {
  list-style: none;
}

img {
  display: block;
  max-width: 100%;
  height: auto;
}

button {
  cursor: pointer;
  background: transparent;
  border: none;
  outline: none;
  font-family: inherit;
}

input, textarea, select {
  outline: none;
  font-family: inherit;
}

/* Layout */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.main {
  min-height: calc(100vh - 150px);
}

/* Header */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: var(--color-bg-primary);
  border-bottom: 1px solid var(--color-border);
  z-index: var(--z-fixed);
  transition: all var(--transition-fast);
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) 0;
}

.navbar__menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.navbar__link {
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
  transition: color var(--transition-fast);
  border-radius: var(--border-radius-md);
}

.navbar__link:hover,
.navbar__link--active {
  color: var(--color-text-primary);
  background-color: var(--color-bg-tertiary);
}

.navbar__link--active {
  color: var(--color-primary);
}

.navbar__actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

/* Logo */
.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  text-decoration: none;
}

.logo__image {
  width: 40px;
  height: 40px;
  object-fit: contain;
}

.logo__text {
  color: var(--color-primary);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--border-radius-md);
  font-family: inherit;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  user-select: none;
}

.btn--primary {
  background-color: var(--color-primary);
  color: var(--color-text-primary);
}

.btn--primary:hover {
  background-color: var(--color-primary-light);
  transform: translateY(-1px);
}

.btn--secondary {
  background-color: transparent;
  color: var(--color-text-secondary);
  border: 1px solid var(--color-border);
}

.btn--secondary:hover {
  background-color: var(--color-bg-tertiary);
  color: var(--color-text-primary);
}

/* Search */
.search-toggle {
  padding: var(--spacing-sm);
  border-radius: var(--border-radius-md);
  background-color: transparent;
  color: var(--color-text-secondary);
  border: 1px solid var(--color-border);
  transition: all var(--transition-fast);
}

.search-toggle:hover {
  background-color: var(--color-bg-tertiary);
  color: var(--color-text-primary);
}

.login-btn {
  background-color: var(--color-primary);
  color: var(--color-text-primary);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);
}

.login-btn:hover {
  background-color: var(--color-primary-light);
  transform: translateY(-1px);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  gap: 4px;
  padding: var(--spacing-sm);
  background: none;
  border: none;
  cursor: pointer;
}

.mobile-menu-toggle span {
  width: 20px;
  height: 2px;
  background-color: var(--color-text-primary);
  transition: all var(--transition-fast);
  border-radius: 1px;
}

/* Icons */
.icon {
  width: 20px;
  height: 20px;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
  fill: none;
}

/* Responsive */
@media (max-width: 991px) {
  .navbar__menu {
    display: none;
  }
  
  .mobile-menu-toggle {
    display: flex;
  }
}

@media (max-width: 767px) {
  .login-btn {
    display: none;
  }
  
  body {
    padding-bottom: 80px;
  }
}

/* Mobile Navigation */
.mobile-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--color-bg-secondary);
  border-top: 1px solid var(--color-border);
  display: flex;
  justify-content: space-around;
  padding: var(--spacing-sm) 0;
  z-index: var(--z-fixed);
}

.mobile-nav__item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: var(--spacing-xs) var(--spacing-sm);
  color: var(--color-text-muted);
  text-decoration: none;
  font-size: var(--font-size-xs);
  border-radius: var(--border-radius-md);
  transition: all var(--transition-fast);
  background: none;
  border: none;
  cursor: pointer;
}

.mobile-nav__item:hover,
.mobile-nav__item--active {
  color: var(--color-primary);
  background-color: var(--color-bg-tertiary);
}

@media (min-width: 992px) {
  .mobile-nav {
    display: none;
  }
  
  body {
    padding-bottom: 0;
  }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.gap-sm { gap: var(--spacing-sm); }
.gap-md { gap: var(--spacing-md); }
.gap-lg { gap: var(--spacing-lg); }
.gap-xl { gap: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-sm); }
.mt-2 { margin-top: var(--spacing-md); }
.mt-3 { margin-top: var(--spacing-lg); }
.mt-4 { margin-top: var(--spacing-xl); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-sm); }
.mb-2 { margin-bottom: var(--spacing-md); }
.mb-3 { margin-bottom: var(--spacing-lg); }
.mb-4 { margin-bottom: var(--spacing-xl); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-sm); }
.p-2 { padding: var(--spacing-md); }
.p-3 { padding: var(--spacing-lg); }
.p-4 { padding: var(--spacing-xl); }

/* Hero Section */
.hero {
  position: relative;
  height: 70vh;
  min-height: 500px;
  overflow: hidden;
}

.hero__slider {
  position: relative;
  width: 100%;
  height: 100%;
}

.hero__slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0;
  transition: opacity var(--transition-slow);
}

.hero__slide.active {
  opacity: 1;
}

.hero__slide::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to right,
    rgba(0, 0, 0, 0.8) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    rgba(0, 0, 0, 0.1) 100%
  );
}

.hero__content {
  position: relative;
  z-index: 2;
  height: 100%;
  display: flex;
  align-items: center;
}

.hero__title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-md);
  color: var(--color-text-primary);
}

.hero__description {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-xl);
  max-width: 600px;
  color: var(--color-text-secondary);
}

.hero__actions {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.hero__controls {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 3;
}

.hero__prev {
  left: var(--spacing-md);
}

.hero__next {
  right: var(--spacing-md);
}

.hero__prev,
.hero__next {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.5);
  color: var(--color-text-primary);
  border: none;
  border-radius: var(--border-radius-full);
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.hero__prev:hover,
.hero__next:hover {
  background-color: rgba(0, 0, 0, 0.8);
  transform: scale(1.1);
}

.hero__indicators {
  position: absolute;
  bottom: var(--spacing-lg);
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: var(--spacing-sm);
  z-index: 3;
}

.hero__indicator {
  width: 12px;
  height: 12px;
  border-radius: var(--border-radius-full);
  background-color: rgba(255, 255, 255, 0.5);
  border: none;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.hero__indicator.active {
  background-color: var(--color-primary);
  transform: scale(1.2);
}

/* Sections */
.section {
  padding: var(--spacing-2xl) 0;
}

.section__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-xl);
}

.section__title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: 0;
}

.section__link {
  color: var(--color-primary);
  font-weight: var(--font-weight-medium);
  transition: color var(--transition-fast);
}

.section__link:hover {
  color: var(--color-primary-light);
}

/* Movie Grid */
.movie-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

@media (max-width: 767px) {
  .movie-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: var(--spacing-sm);
  }
}

/* Movie Card */
.movie-card {
  position: relative;
  background-color: var(--color-bg-card);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  transition: all var(--transition-normal);
  cursor: pointer;
}

.movie-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.movie-card__poster {
  position: relative;
  aspect-ratio: 2/3;
  overflow: hidden;
}

.movie-card__poster img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-normal);
}

.movie-card:hover .movie-card__poster img {
  transform: scale(1.05);
}

.movie-card__overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.movie-card:hover .movie-card__overlay {
  opacity: 1;
}

.movie-card__actions {
  display: flex;
  gap: var(--spacing-sm);
}

.movie-card__info {
  padding: var(--spacing-md);
}

.movie-card__title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-sm);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.movie-card__meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: var(--font-size-sm);
  color: var(--color-text-muted);
}

.movie-card__rating {
  color: var(--color-accent-gold);
}

/* Button Sizes */
.btn--sm {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-sm);
}

.btn--lg {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-lg);
}

/* Features Section */
.features {
  background-color: var(--color-bg-secondary);
  padding: var(--spacing-4xl) 0;
}

.features__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-xl);
}

.feature {
  text-align: center;
  padding: var(--spacing-xl);
}

.feature__icon {
  margin-bottom: var(--spacing-lg);
  color: var(--color-primary);
}

.feature__icon .icon {
  width: 48px;
  height: 48px;
}

.feature__title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-md);
  color: var(--color-text-primary);
}

.feature__description {
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
}

/* Footer */
.footer {
  background-color: var(--color-bg-secondary);
  border-top: 1px solid var(--color-border);
  padding: var(--spacing-2xl) 0 var(--spacing-xl);
}

.footer__content {
  display: grid;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

.footer__brand {
  margin-bottom: var(--spacing-lg);
}

.footer__description {
  color: var(--color-text-secondary);
  margin-top: var(--spacing-md);
}

.footer__links {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
}

.footer__title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-md);
}

.footer__list {
  list-style: none;
  padding: 0;
}

.footer__list li {
  margin-bottom: var(--spacing-sm);
}

.footer__list a {
  color: var(--color-text-secondary);
  transition: color var(--transition-fast);
}

.footer__list a:hover {
  color: var(--color-primary);
}

.footer__bottom {
  border-top: 1px solid var(--color-border);
  padding-top: var(--spacing-lg);
  text-align: center;
}

.footer__copyright {
  color: var(--color-text-muted);
  font-size: var(--font-size-sm);
}

/* Search Results */
.search-result-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--color-border);
  transition: background-color var(--transition-fast);
}

.search-result-item:hover {
  background-color: var(--color-bg-card);
}

.search-result-poster {
  width: 60px;
  height: 90px;
  object-fit: cover;
  border-radius: var(--border-radius-md);
}

.search-result-info {
  flex: 1;
}

.search-result-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-xs);
}

.search-result-link {
  color: var(--color-primary);
  font-size: var(--font-size-sm);
}

.search-loading,
.search-no-results {
  padding: var(--spacing-lg);
  text-align: center;
  color: var(--color-text-secondary);
}

/* Loading */
.loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
}

.loading__spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--color-border);
  border-top: 4px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
