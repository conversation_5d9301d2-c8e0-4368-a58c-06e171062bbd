<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="ดูหนังออนไลน์ฟรี หนังใหม่ 2025 ไม่มีโฆษณา ชัดที่สุด ไวที่สุด">
    <meta name="keywords" content="ดูหนัง, หนังออนไลน์, หนังใหม่, ซีรี่ย์, อนิเมะ, ไม่มีโฆษณา">
    <title>DoMovie - ดูหนังออนไลน์ฟรี หนังใหม่ 2025 ไม่มีโฆษณา</title>
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Thai:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    
    <!-- Styles -->
    <link rel="stylesheet" href="dist/css/style.css">
    
    <!-- Open Graph -->
    <meta property="og:title" content="DoMovie - ดูหนังออนไลน์ฟรี">
    <meta property="og:description" content="ดูหนังออนไลน์ฟรี หนังใหม่ 2025 ไม่มีโฆษณา ชัดที่สุด ไวที่สุด">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://doomovie.com">
    <meta property="og:image" content="/og-image.jpg">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <!-- Logo -->
                <div class="navbar__brand">
                    <a href="/" class="logo">
                        <span class="logo__text">DoMovie</span>
                    </a>
                </div>
                
                <!-- Desktop Navigation -->
                <ul class="navbar__menu">
                    <li class="navbar__item">
                        <a href="/" class="navbar__link navbar__link--active">หน้าหลัก</a>
                    </li>
                    <li class="navbar__item">
                        <a href="/movies/หนังฝรั่ง" class="navbar__link">หนังฝรั่ง</a>
                    </li>
                    <li class="navbar__item">
                        <a href="/movies/ซีรี่ย์" class="navbar__link">ซีรี่ย์</a>
                    </li>
                    <li class="navbar__item">
                        <a href="/movies/อนิเมะ" class="navbar__link">อนิเมะ</a>
                    </li>
                    <li class="navbar__item">
                        <a href="/categories" class="navbar__link">ประเภทหนัง</a>
                    </li>
                    <li class="navbar__item">
                        <a href="/movies/คลิปไวรัล" class="navbar__link">คลิปไวรัล</a>
                    </li>
                    <li class="navbar__item">
                        <a href="/tags" class="navbar__link">แท็กหนัง</a>
                    </li>
                </ul>
                
                <!-- Search & User Actions -->
                <div class="navbar__actions">
                    <button class="search-toggle" aria-label="เปิดการค้นหา">
                        <svg class="icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <circle cx="11" cy="11" r="8"></circle>
                            <path d="m21 21-4.35-4.35"></path>
                        </svg>
                    </button>
                    
                    <button class="login-btn" data-modal="login">เข้าสู่ระบบ</button>
                    
                    <!-- Mobile menu toggle -->
                    <button class="mobile-menu-toggle" aria-label="เปิดเมนู">
                        <span></span>
                        <span></span>
                        <span></span>
                    </button>
                </div>
            </nav>
            
            <!-- Search Bar -->
            <div class="search-bar" id="searchBar">
                <div class="search-bar__container">
                    <input type="text" class="search-bar__input" placeholder="ค้นหาหนัง, ซีรี่ย์, อนิเมะ..." id="searchInput">
                    <button class="search-bar__submit" type="submit">
                        <svg class="icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <circle cx="11" cy="11" r="8"></circle>
                            <path d="m21 21-4.35-4.35"></path>
                        </svg>
                    </button>
                    <button class="search-bar__close" id="searchClose">
                        <svg class="icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <line x1="18" y1="6" x2="6" y2="18"></line>
                            <line x1="6" y1="6" x2="18" y2="18"></line>
                        </svg>
                    </button>
                </div>
                <div class="search-results" id="searchResults"></div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <!-- Hero Section -->
        <section class="hero">
            <div class="hero__slider" id="heroSlider">
                <!-- Hero slides will be populated by JavaScript -->
            </div>
            <div class="hero__controls">
                <button class="hero__prev" aria-label="ภาพก่อนหน้า">
                    <svg class="icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <polyline points="15,18 9,12 15,6"></polyline>
                    </svg>
                </button>
                <button class="hero__next" aria-label="ภาพถัดไป">
                    <svg class="icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <polyline points="9,18 15,12 9,6"></polyline>
                    </svg>
                </button>
            </div>
            <div class="hero__indicators" id="heroIndicators"></div>
        </section>

        <!-- Featured Movies -->
        <section class="section">
            <div class="container">
                <h2 class="section__title">หนังมาแรง</h2>
                <div class="movie-grid" id="featuredMovies">
                    <!-- Movie cards will be populated by JavaScript -->
                </div>
            </div>
        </section>

        <!-- Top 10 Weekly -->
        <section class="section">
            <div class="container">
                <h2 class="section__title">Top 10 ประจำสัปดาห์</h2>
                <div class="movie-grid" id="topWeekly">
                    <!-- Movie cards will be populated by JavaScript -->
                </div>
            </div>
        </section>

        <!-- New Movies -->
        <section class="section">
            <div class="container">
                <div class="section__header">
                    <h2 class="section__title">หนังมาใหม่</h2>
                    <a href="/movies/หนังมาใหม่" class="section__link">ดูทั้งหมด</a>
                </div>
                <div class="movie-grid" id="newMovies">
                    <!-- Movie cards will be populated by JavaScript -->
                </div>
            </div>
        </section>

        <!-- Top 10 Must Watch -->
        <section class="section">
            <div class="container">
                <h2 class="section__title">Top 10 ไม่ควรพลาด</h2>
                <div class="movie-grid" id="mustWatch">
                    <!-- Movie cards will be populated by JavaScript -->
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section class="features">
            <div class="container">
                <div class="features__grid">
                    <div class="feature">
                        <div class="feature__icon">
                            <svg class="icon" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
                            </svg>
                        </div>
                        <h3 class="feature__title">อัปเดตทุกวัน</h3>
                        <p class="feature__description">เราอัปเดทหนังใหม่ทุกวัน ทั้งหนังไทย หนังฝรั่ง ซีรีย์ดัง การ์ตูนสำหรับเด็ก เพื่อความเพลิดเพลินอย่างไร้ขีดจำกัด 24 ชม.</p>
                    </div>
                    
                    <div class="feature">
                        <div class="feature__icon">
                            <svg class="icon" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                                <line x1="8" y1="21" x2="16" y2="21"></line>
                                <line x1="12" y1="17" x2="12" y2="21"></line>
                            </svg>
                        </div>
                        <h3 class="feature__title">ดูได้ทุกที่</h3>
                        <p class="feature__description">สะดวกสบาย ไม่ว่าที่ไหนก็รับชมได้ ทั้ง Smart Phone, Tablet, Laptop และอีกมาก ภาพชัดทั้งเรื่อง เสียงใสเต็มระบบ Full HD</p>
                    </div>
                    
                    <div class="feature">
                        <div class="feature__icon">
                            <svg class="icon" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M9 12l2 2 4-4"></path>
                                <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"></path>
                                <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"></path>
                                <path d="M12 3c0 1-1 3-3 3s-3-2-3-3 1-3 3-3 3 2 3 3"></path>
                                <path d="M12 21c0-1 1-3 3-3s3 2 3 3-1 3-3 3-3-2-3-3"></path>
                            </svg>
                        </div>
                        <h3 class="feature__title">ดูฟรีไม่ต้องสมัครสมาชิก</h3>
                        <p class="feature__description">ดูได้ทั้งครอบครัว ทุกช่วงอายุ ทุกเพศ ทุกวัย ใช้ช่วงเวลาพักผ่อนของคุณให้คุ้มค่า ติดตามหนังใหม่กับเว็บ DoMovie</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer__content">
                <div class="footer__brand">
                    <a href="/" class="logo">
                        <span class="logo__text">DoMovie</span>
                    </a>
                    <p class="footer__description">
                        ดูหนังออนไลน์ฟรี ไม่มีโฆษณา ครบทุกรส เต็มอารมณ์
                    </p>
                </div>
                
                <div class="footer__links">
                    <div class="footer__column">
                        <h4 class="footer__title">หมวดหมู่</h4>
                        <ul class="footer__list">
                            <li><a href="/movies/หนังฝรั่ง">หนังฝรั่ง</a></li>
                            <li><a href="/movies/หนังไทย">หนังไทย</a></li>
                            <li><a href="/movies/ซีรี่ย์">ซีรี่ย์</a></li>
                            <li><a href="/movies/อนิเมะ">อนิเมะ</a></li>
                        </ul>
                    </div>
                    
                    <div class="footer__column">
                        <h4 class="footer__title">ประเภท</h4>
                        <ul class="footer__list">
                            <li><a href="/movies/แอคชั่น">แอคชั่น</a></li>
                            <li><a href="/movies/ดราม่า">ดราม่า</a></li>
                            <li><a href="/movies/ตลก">ตลก</a></li>
                            <li><a href="/movies/สยองขวัญ">สยองขวัญ</a></li>
                        </ul>
                    </div>
                    
                    <div class="footer__column">
                        <h4 class="footer__title">เกี่ยวกับเรา</h4>
                        <ul class="footer__list">
                            <li><a href="/about">เกี่ยวกับเรา</a></li>
                            <li><a href="/contact">ติดต่อเรา</a></li>
                            <li><a href="/privacy">นโยบายความเป็นส่วนตัว</a></li>
                            <li><a href="/terms">ข้อกำหนดการใช้งาน</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="footer__bottom">
                <p class="footer__copyright">
                    &copy; 2025 DoMovie. All rights reserved.
                </p>
            </div>
        </div>
    </footer>

    <!-- Mobile Navigation -->
    <nav class="mobile-nav">
        <a href="/" class="mobile-nav__item mobile-nav__item--active">
            <svg class="icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                <polyline points="9,22 9,12 15,12 15,22"></polyline>
            </svg>
            <span>หน้าแรก</span>
        </a>
        
        <button class="mobile-nav__item search-toggle">
            <svg class="icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <circle cx="11" cy="11" r="8"></circle>
                <path d="m21 21-4.35-4.35"></path>
            </svg>
            <span>ค้นหา</span>
        </button>
        
        <a href="/categories" class="mobile-nav__item">
            <svg class="icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <rect x="3" y="3" width="7" height="7"></rect>
                <rect x="14" y="3" width="7" height="7"></rect>
                <rect x="14" y="14" width="7" height="7"></rect>
                <rect x="3" y="14" width="7" height="7"></rect>
            </svg>
            <span>ประเภท</span>
        </a>
    </nav>

    <!-- Modals will be inserted here by JavaScript -->
    <div id="modalContainer"></div>

    <!-- Loading indicator -->
    <div class="loading" id="loading">
        <div class="loading__spinner"></div>
    </div>
    
    <!-- Scripts -->
    <script src="dist/js/main.js"></script>
</body>
</html>
