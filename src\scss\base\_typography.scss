/* Typography */

/* Headings */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-md);
}

h1 {
  font-size: var(--font-size-4xl);
  
  @include respond-to(md) {
    font-size: var(--font-size-5xl);
  }
}

h2 {
  font-size: var(--font-size-3xl);
  
  @include respond-to(md) {
    font-size: var(--font-size-4xl);
  }
}

h3 {
  font-size: var(--font-size-2xl);
  
  @include respond-to(md) {
    font-size: var(--font-size-3xl);
  }
}

h4 {
  font-size: var(--font-size-xl);
  
  @include respond-to(md) {
    font-size: var(--font-size-2xl);
  }
}

h5 {
  font-size: var(--font-size-lg);
  
  @include respond-to(md) {
    font-size: var(--font-size-xl);
  }
}

h6 {
  font-size: var(--font-size-base);
  
  @include respond-to(md) {
    font-size: var(--font-size-lg);
  }
}

/* Paragraphs */
p {
  margin-bottom: var(--spacing-md);
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
}

/* Links */
a {
  color: var(--color-primary);
  transition: color var(--transition-fast);
  
  &:hover {
    color: var(--color-primary-light);
  }
  
  &:focus {
    @include focus-ring;
  }
}

/* Lists */
ul, ol {
  margin-bottom: var(--spacing-md);
  padding-left: var(--spacing-lg);
}

li {
  margin-bottom: var(--spacing-xs);
  color: var(--color-text-secondary);
}

/* Blockquotes */
blockquote {
  margin: var(--spacing-lg) 0;
  padding: var(--spacing-md) var(--spacing-lg);
  border-left: 4px solid var(--color-primary);
  background-color: var(--color-bg-tertiary);
  font-style: italic;
  color: var(--color-text-secondary);
}

/* Code */
code {
  font-family: var(--font-mono);
  font-size: 0.875em;
  background-color: var(--color-bg-tertiary);
  padding: 0.125em 0.25em;
  border-radius: var(--border-radius-sm);
  color: var(--color-accent-orange);
}

pre {
  font-family: var(--font-mono);
  background-color: var(--color-bg-tertiary);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  overflow-x: auto;
  margin-bottom: var(--spacing-md);
  
  code {
    background: none;
    padding: 0;
    color: var(--color-text-primary);
  }
}

/* Small text */
small {
  font-size: var(--font-size-sm);
  color: var(--color-text-muted);
}

/* Strong and emphasis */
strong, b {
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
}

em, i {
  font-style: italic;
}

/* Mark */
mark {
  background-color: var(--color-accent-gold);
  color: var(--color-text-inverse);
  padding: 0.125em 0.25em;
  border-radius: var(--border-radius-sm);
}

/* Utility classes */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }
.text-4xl { font-size: var(--font-size-4xl); }
.text-5xl { font-size: var(--font-size-5xl); }

.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }
.font-extrabold { font-weight: var(--font-weight-extrabold); }

.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

.text-primary { color: var(--color-text-primary); }
.text-secondary { color: var(--color-text-secondary); }
.text-muted { color: var(--color-text-muted); }
.text-accent { color: var(--color-primary); }
.text-success { color: var(--color-success); }
.text-warning { color: var(--color-warning); }
.text-error { color: var(--color-error); }

.text-truncate {
  @include text-truncate;
}

.text-clamp-1 {
  @include text-clamp(1);
}

.text-clamp-2 {
  @include text-clamp(2);
}

.text-clamp-3 {
  @include text-clamp(3);
}

/* Line height utilities */
.leading-tight { line-height: var(--line-height-tight); }
.leading-normal { line-height: var(--line-height-normal); }
.leading-relaxed { line-height: var(--line-height-relaxed); }

/* Letter spacing */
.tracking-tight { letter-spacing: -0.025em; }
.tracking-normal { letter-spacing: 0; }
.tracking-wide { letter-spacing: 0.025em; }

/* Text transform */
.uppercase { text-transform: uppercase; }
.lowercase { text-transform: lowercase; }
.capitalize { text-transform: capitalize; }
.normal-case { text-transform: none; }
