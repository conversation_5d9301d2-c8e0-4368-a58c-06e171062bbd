<?php
/**
 * Custom Post Types for DoMovie Theme
 * 
 * @package DoMovie_Theme
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Register Movie Post Type
 */
function doomovie_register_movie_post_type() {
    $labels = array(
        'name'                  => _x('Movies', 'Post type general name', 'doomovie-theme'),
        'singular_name'         => _x('Movie', 'Post type singular name', 'doomovie-theme'),
        'menu_name'             => _x('Movies', 'Admin Menu text', 'doomovie-theme'),
        'name_admin_bar'        => _x('Movie', 'Add New on Toolbar', 'doomovie-theme'),
        'add_new'               => __('Add New', 'doomovie-theme'),
        'add_new_item'          => __('Add New Movie', 'doomovie-theme'),
        'new_item'              => __('New Movie', 'doomovie-theme'),
        'edit_item'             => __('Edit Movie', 'doomovie-theme'),
        'view_item'             => __('View Movie', 'doomovie-theme'),
        'all_items'             => __('All Movies', 'doomovie-theme'),
        'search_items'          => __('Search Movies', 'doomovie-theme'),
        'parent_item_colon'     => __('Parent Movies:', 'doomovie-theme'),
        'not_found'             => __('No movies found.', 'doomovie-theme'),
        'not_found_in_trash'    => __('No movies found in Trash.', 'doomovie-theme'),
        'featured_image'        => _x('Movie Poster', 'Overrides the "Featured Image" phrase', 'doomovie-theme'),
        'set_featured_image'    => _x('Set movie poster', 'Overrides the "Set featured image" phrase', 'doomovie-theme'),
        'remove_featured_image' => _x('Remove movie poster', 'Overrides the "Remove featured image" phrase', 'doomovie-theme'),
        'use_featured_image'    => _x('Use as movie poster', 'Overrides the "Use as featured image" phrase', 'doomovie-theme'),
        'archives'              => _x('Movie archives', 'The post type archive label', 'doomovie-theme'),
        'insert_into_item'      => _x('Insert into movie', 'Overrides the "Insert into post" phrase', 'doomovie-theme'),
        'uploaded_to_this_item' => _x('Uploaded to this movie', 'Overrides the "Uploaded to this post" phrase', 'doomovie-theme'),
        'filter_items_list'     => _x('Filter movies list', 'Screen reader text for the filter links', 'doomovie-theme'),
        'items_list_navigation' => _x('Movies list navigation', 'Screen reader text for the pagination', 'doomovie-theme'),
        'items_list'            => _x('Movies list', 'Screen reader text for the items list', 'doomovie-theme'),
    );

    $args = array(
        'labels'             => $labels,
        'public'             => true,
        'publicly_queryable' => true,
        'show_ui'            => true,
        'show_in_menu'       => true,
        'show_in_nav_menus'  => true,
        'show_in_admin_bar'  => true,
        'show_in_rest'       => true,
        'query_var'          => true,
        'rewrite'            => array('slug' => 'movie'),
        'capability_type'    => 'post',
        'has_archive'        => true,
        'hierarchical'       => false,
        'menu_position'      => 5,
        'menu_icon'          => 'dashicons-video-alt3',
        'supports'           => array(
            'title',
            'editor',
            'thumbnail',
            'excerpt',
            'custom-fields',
            'comments',
            'revisions',
            'author',
            'page-attributes'
        ),
        'taxonomies'         => array('movie_genre', 'movie_country', 'movie_year', 'movie_quality', 'movie_language'),
        'can_export'         => true,
        'delete_with_user'   => false,
    );

    register_post_type('movie', $args);
}
add_action('init', 'doomovie_register_movie_post_type');

/**
 * Register Series Post Type
 */
function doomovie_register_series_post_type() {
    $labels = array(
        'name'                  => _x('Series', 'Post type general name', 'doomovie-theme'),
        'singular_name'         => _x('Series', 'Post type singular name', 'doomovie-theme'),
        'menu_name'             => _x('Series', 'Admin Menu text', 'doomovie-theme'),
        'name_admin_bar'        => _x('Series', 'Add New on Toolbar', 'doomovie-theme'),
        'add_new'               => __('Add New', 'doomovie-theme'),
        'add_new_item'          => __('Add New Series', 'doomovie-theme'),
        'new_item'              => __('New Series', 'doomovie-theme'),
        'edit_item'             => __('Edit Series', 'doomovie-theme'),
        'view_item'             => __('View Series', 'doomovie-theme'),
        'all_items'             => __('All Series', 'doomovie-theme'),
        'search_items'          => __('Search Series', 'doomovie-theme'),
        'parent_item_colon'     => __('Parent Series:', 'doomovie-theme'),
        'not_found'             => __('No series found.', 'doomovie-theme'),
        'not_found_in_trash'    => __('No series found in Trash.', 'doomovie-theme'),
        'featured_image'        => _x('Series Poster', 'Overrides the "Featured Image" phrase', 'doomovie-theme'),
        'set_featured_image'    => _x('Set series poster', 'Overrides the "Set featured image" phrase', 'doomovie-theme'),
        'remove_featured_image' => _x('Remove series poster', 'Overrides the "Remove featured image" phrase', 'doomovie-theme'),
        'use_featured_image'    => _x('Use as series poster', 'Overrides the "Use as featured image" phrase', 'doomovie-theme'),
        'archives'              => _x('Series archives', 'The post type archive label', 'doomovie-theme'),
        'insert_into_item'      => _x('Insert into series', 'Overrides the "Insert into post" phrase', 'doomovie-theme'),
        'uploaded_to_this_item' => _x('Uploaded to this series', 'Overrides the "Uploaded to this post" phrase', 'doomovie-theme'),
        'filter_items_list'     => _x('Filter series list', 'Screen reader text for the filter links', 'doomovie-theme'),
        'items_list_navigation' => _x('Series list navigation', 'Screen reader text for the pagination', 'doomovie-theme'),
        'items_list'            => _x('Series list', 'Screen reader text for the items list', 'doomovie-theme'),
    );

    $args = array(
        'labels'             => $labels,
        'public'             => true,
        'publicly_queryable' => true,
        'show_ui'            => true,
        'show_in_menu'       => true,
        'show_in_nav_menus'  => true,
        'show_in_admin_bar'  => true,
        'show_in_rest'       => true,
        'query_var'          => true,
        'rewrite'            => array('slug' => 'series'),
        'capability_type'    => 'post',
        'has_archive'        => true,
        'hierarchical'       => false,
        'menu_position'      => 6,
        'menu_icon'          => 'dashicons-playlist-video',
        'supports'           => array(
            'title',
            'editor',
            'thumbnail',
            'excerpt',
            'custom-fields',
            'comments',
            'revisions',
            'author',
            'page-attributes'
        ),
        'taxonomies'         => array('series_genre', 'series_country', 'series_year', 'series_status'),
        'can_export'         => true,
        'delete_with_user'   => false,
    );

    register_post_type('series', $args);
}
add_action('init', 'doomovie_register_series_post_type');

/**
 * Register Episode Post Type
 */
function doomovie_register_episode_post_type() {
    $labels = array(
        'name'                  => _x('Episodes', 'Post type general name', 'doomovie-theme'),
        'singular_name'         => _x('Episode', 'Post type singular name', 'doomovie-theme'),
        'menu_name'             => _x('Episodes', 'Admin Menu text', 'doomovie-theme'),
        'name_admin_bar'        => _x('Episode', 'Add New on Toolbar', 'doomovie-theme'),
        'add_new'               => __('Add New', 'doomovie-theme'),
        'add_new_item'          => __('Add New Episode', 'doomovie-theme'),
        'new_item'              => __('New Episode', 'doomovie-theme'),
        'edit_item'             => __('Edit Episode', 'doomovie-theme'),
        'view_item'             => __('View Episode', 'doomovie-theme'),
        'all_items'             => __('All Episodes', 'doomovie-theme'),
        'search_items'          => __('Search Episodes', 'doomovie-theme'),
        'parent_item_colon'     => __('Parent Episodes:', 'doomovie-theme'),
        'not_found'             => __('No episodes found.', 'doomovie-theme'),
        'not_found_in_trash'    => __('No episodes found in Trash.', 'doomovie-theme'),
        'featured_image'        => _x('Episode Thumbnail', 'Overrides the "Featured Image" phrase', 'doomovie-theme'),
        'set_featured_image'    => _x('Set episode thumbnail', 'Overrides the "Set featured image" phrase', 'doomovie-theme'),
        'remove_featured_image' => _x('Remove episode thumbnail', 'Overrides the "Remove featured image" phrase', 'doomovie-theme'),
        'use_featured_image'    => _x('Use as episode thumbnail', 'Overrides the "Use as featured image" phrase', 'doomovie-theme'),
        'archives'              => _x('Episode archives', 'The post type archive label', 'doomovie-theme'),
        'insert_into_item'      => _x('Insert into episode', 'Overrides the "Insert into post" phrase', 'doomovie-theme'),
        'uploaded_to_this_item' => _x('Uploaded to this episode', 'Overrides the "Uploaded to this post" phrase', 'doomovie-theme'),
        'filter_items_list'     => _x('Filter episodes list', 'Screen reader text for the filter links', 'doomovie-theme'),
        'items_list_navigation' => _x('Episodes list navigation', 'Screen reader text for the pagination', 'doomovie-theme'),
        'items_list'            => _x('Episodes list', 'Screen reader text for the items list', 'doomovie-theme'),
    );

    $args = array(
        'labels'             => $labels,
        'public'             => true,
        'publicly_queryable' => true,
        'show_ui'            => true,
        'show_in_menu'       => 'edit.php?post_type=series',
        'show_in_nav_menus'  => false,
        'show_in_admin_bar'  => true,
        'show_in_rest'       => true,
        'query_var'          => true,
        'rewrite'            => array('slug' => 'episode'),
        'capability_type'    => 'post',
        'has_archive'        => false,
        'hierarchical'       => false,
        'menu_position'      => null,
        'supports'           => array(
            'title',
            'editor',
            'thumbnail',
            'excerpt',
            'custom-fields',
            'comments',
            'revisions',
            'author',
            'page-attributes'
        ),
        'can_export'         => true,
        'delete_with_user'   => false,
    );

    register_post_type('episode', $args);
}
add_action('init', 'doomovie_register_episode_post_type');

/**
 * Flush rewrite rules on theme activation
 */
function doomovie_flush_rewrite_rules() {
    doomovie_register_movie_post_type();
    doomovie_register_series_post_type();
    doomovie_register_episode_post_type();
    flush_rewrite_rules();
}
add_action('after_switch_theme', 'doomovie_flush_rewrite_rules');

/**
 * Add custom columns to movie admin list
 */
function doomovie_movie_admin_columns($columns) {
    $new_columns = array();
    $new_columns['cb'] = $columns['cb'];
    $new_columns['poster'] = __('Poster', 'doomovie-theme');
    $new_columns['title'] = $columns['title'];
    $new_columns['movie_genre'] = __('Genres', 'doomovie-theme');
    $new_columns['movie_year'] = __('Year', 'doomovie-theme');
    $new_columns['imdb_rating'] = __('IMDB', 'doomovie-theme');
    $new_columns['view_count'] = __('Views', 'doomovie-theme');
    $new_columns['featured'] = __('Featured', 'doomovie-theme');
    $new_columns['date'] = $columns['date'];
    
    return $new_columns;
}
add_filter('manage_movie_posts_columns', 'doomovie_movie_admin_columns');

/**
 * Display custom column content
 */
function doomovie_movie_admin_column_content($column, $post_id) {
    switch ($column) {
        case 'poster':
            $poster = get_the_post_thumbnail($post_id, array(50, 75));
            echo $poster ? $poster : '<span class="dashicons dashicons-format-image"></span>';
            break;
            
        case 'movie_genre':
            $genres = get_the_terms($post_id, 'movie_genre');
            if ($genres && !is_wp_error($genres)) {
                $genre_names = wp_list_pluck($genres, 'name');
                echo implode(', ', $genre_names);
            } else {
                echo '—';
            }
            break;
            
        case 'movie_year':
            $year = get_post_meta($post_id, 'release_year', true);
            echo $year ? $year : '—';
            break;
            
        case 'imdb_rating':
            $rating = get_post_meta($post_id, 'imdb_rating', true);
            echo $rating ? $rating : '—';
            break;
            
        case 'view_count':
            $views = get_post_meta($post_id, 'view_count', true);
            echo $views ? number_format($views) : '0';
            break;
            
        case 'featured':
            $featured = get_post_meta($post_id, 'featured', true);
            if ($featured === '1') {
                echo '<span class="dashicons dashicons-star-filled" style="color: #ffb900;"></span>';
            } else {
                echo '<span class="dashicons dashicons-star-empty"></span>';
            }
            break;
    }
}
add_action('manage_movie_posts_custom_column', 'doomovie_movie_admin_column_content', 10, 2);

/**
 * Make custom columns sortable
 */
function doomovie_movie_sortable_columns($columns) {
    $columns['movie_year'] = 'release_year';
    $columns['imdb_rating'] = 'imdb_rating';
    $columns['view_count'] = 'view_count';
    $columns['featured'] = 'featured';
    
    return $columns;
}
add_filter('manage_edit-movie_sortable_columns', 'doomovie_movie_sortable_columns');

/**
 * Handle sorting for custom columns
 */
function doomovie_movie_orderby($query) {
    if (!is_admin() || !$query->is_main_query()) {
        return;
    }

    $orderby = $query->get('orderby');

    switch ($orderby) {
        case 'release_year':
        case 'imdb_rating':
        case 'view_count':
        case 'featured':
            $query->set('meta_key', $orderby);
            $query->set('orderby', 'meta_value_num');
            break;
    }
}
add_action('pre_get_posts', 'doomovie_movie_orderby');
