# การวิเคราะห์ WordPress และ Backend ของ EZMovie.me

## WordPress Structure

### Theme Structure (สันนิษฐาน)
```
wp-content/
├── themes/
│   └── ezmovie-theme/
│       ├── style.css
│       ├── functions.php
│       ├── index.php
│       ├── single-movie.php
│       ├── archive-movie.php
│       ├── page-categories.php
│       ├── header.php
│       ├── footer.php
│       ├── sidebar.php
│       ├── assets/
│       │   ├── css/
│       │   ├── js/
│       │   └── img/
│       └── template-parts/
│           ├── movie-card.php
│           ├── hero-slider.php
│           └── navigation.php
```

### Custom Post Types
```php
// functions.php
function register_movie_post_type() {
    register_post_type('movie', array(
        'labels' => array(
            'name' => 'Movies',
            'singular_name' => 'Movie'
        ),
        'public' => true,
        'has_archive' => true,
        'supports' => array('title', 'editor', 'thumbnail', 'custom-fields'),
        'menu_icon' => 'dashicons-video-alt3',
        'rewrite' => array('slug' => 'movie')
    ));
}
add_action('init', 'register_movie_post_type');
```

### Custom Taxonomies
```php
// Movie Genres
function register_movie_taxonomies() {
    // Genre taxonomy
    register_taxonomy('movie_genre', 'movie', array(
        'labels' => array(
            'name' => 'Genres',
            'singular_name' => 'Genre'
        ),
        'hierarchical' => true,
        'rewrite' => array('slug' => 'movies')
    ));
    
    // Country taxonomy
    register_taxonomy('movie_country', 'movie', array(
        'labels' => array(
            'name' => 'Countries',
            'singular_name' => 'Country'
        ),
        'hierarchical' => true
    ));
    
    // Year taxonomy
    register_taxonomy('movie_year', 'movie', array(
        'labels' => array(
            'name' => 'Years',
            'singular_name' => 'Year'
        ),
        'hierarchical' => false
    ));
    
    // Quality taxonomy
    register_taxonomy('movie_quality', 'movie', array(
        'labels' => array(
            'name' => 'Quality',
            'singular_name' => 'Quality'
        ),
        'hierarchical' => false
    ));
}
add_action('init', 'register_movie_taxonomies');
```

### Custom Fields (ACF หรือ Meta Boxes)
```php
// Movie meta fields
$movie_meta_fields = array(
    'imdb_rating' => 'IMDB Rating',
    'duration' => 'Duration (minutes)',
    'release_year' => 'Release Year',
    'age_rating' => 'Age Rating',
    'video_url_thai_1' => 'Video URL Thai 1',
    'video_url_thai_2' => 'Video URL Thai 2',
    'video_url_sub' => 'Video URL Subtitle',
    'trailer_url' => 'Trailer URL',
    'poster_url' => 'Poster URL',
    'backdrop_url' => 'Backdrop URL',
    'actors' => 'Actors (comma separated)',
    'director' => 'Director',
    'synopsis' => 'Synopsis',
    'featured' => 'Featured Movie',
    'coming_soon' => 'Coming Soon',
    'top_movie' => 'Top Movie'
);
```

## Plugins ที่น่าจะใช้

### Essential Plugins
1. **Advanced Custom Fields (ACF)** - จัดการ custom fields
2. **Custom Post Type UI** - สร้าง custom post types
3. **Yoast SEO** - SEO optimization
4. **W3 Total Cache** หรือ **WP Rocket** - Caching
5. **Cloudflare** - CDN และ security
6. **Wordfence Security** - Security
7. **UpdraftPlus** - Backup

### Movie-specific Plugins
1. **WP Video Gallery** - Video management
2. **Meta Slider** - Hero slider
3. **Ajax Search Lite** - Search functionality
4. **WP User Frontend** - User submissions
5. **Ultimate Member** - User management
6. **WooCommerce** (ถ้ามี premium features)

### Custom Plugins (สันนิษฐาน)
```php
// ezmovie-core/ezmovie-core.php
class EZMovieCore {
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('wp_ajax_search_movies', array($this, 'ajax_search_movies'));
        add_action('wp_ajax_nopriv_search_movies', array($this, 'ajax_search_movies'));
        add_action('wp_ajax_report_problem', array($this, 'ajax_report_problem'));
        add_action('wp_ajax_add_to_watchlist', array($this, 'ajax_add_to_watchlist'));
    }
    
    public function ajax_search_movies() {
        // Handle AJAX search
    }
    
    public function ajax_report_problem() {
        // Handle problem reporting
    }
    
    public function ajax_add_to_watchlist() {
        // Handle watchlist management
    }
}
new EZMovieCore();
```

## Database Structure

### Custom Tables (สันนิษฐาน)
```sql
-- Movie views tracking
CREATE TABLE ez_movie_views (
    id INT AUTO_INCREMENT PRIMARY KEY,
    movie_id INT NOT NULL,
    user_ip VARCHAR(45),
    user_agent TEXT,
    view_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_movie_id (movie_id),
    INDEX idx_view_date (view_date)
);

-- User watchlist
CREATE TABLE ez_user_watchlist (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    movie_id INT NOT NULL,
    added_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user_movie (user_id, movie_id)
);

-- Problem reports
CREATE TABLE ez_problem_reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    movie_id INT NOT NULL,
    problem_type VARCHAR(50),
    user_phone VARCHAR(20),
    report_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    status ENUM('pending', 'resolved', 'ignored') DEFAULT 'pending'
);

-- Movie requests
CREATE TABLE ez_movie_requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    movie_title VARCHAR(255),
    user_phone VARCHAR(20),
    request_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    status ENUM('pending', 'added', 'rejected') DEFAULT 'pending'
);
```

### WordPress Tables Customization
```sql
-- wp_posts (movies stored here)
-- wp_postmeta (movie metadata)
-- wp_terms (genres, countries, years)
-- wp_term_taxonomy (taxonomy definitions)
-- wp_term_relationships (movie-taxonomy relationships)
-- wp_users (user accounts)
-- wp_usermeta (user preferences, watchlist)
```

## API Endpoints

### REST API (WordPress REST API Extended)
```php
// Custom REST endpoints
function register_movie_api_routes() {
    register_rest_route('ezmovie/v1', '/movies', array(
        'methods' => 'GET',
        'callback' => 'get_movies_api',
        'args' => array(
            'genre' => array('type' => 'string'),
            'year' => array('type' => 'integer'),
            'country' => array('type' => 'string'),
            'search' => array('type' => 'string'),
            'page' => array('type' => 'integer', 'default' => 1),
            'per_page' => array('type' => 'integer', 'default' => 20)
        )
    ));
    
    register_rest_route('ezmovie/v1', '/movies/(?P<id>\d+)', array(
        'methods' => 'GET',
        'callback' => 'get_movie_api'
    ));
    
    register_rest_route('ezmovie/v1', '/search', array(
        'methods' => 'GET',
        'callback' => 'search_movies_api'
    ));
}
add_action('rest_api_init', 'register_movie_api_routes');
```

### AJAX Handlers
```php
// Search functionality
function ajax_search_movies() {
    $search_term = sanitize_text_field($_POST['search']);
    $args = array(
        'post_type' => 'movie',
        's' => $search_term,
        'posts_per_page' => 10
    );
    
    $movies = new WP_Query($args);
    $results = array();
    
    while ($movies->have_posts()) {
        $movies->the_post();
        $results[] = array(
            'id' => get_the_ID(),
            'title' => get_the_title(),
            'url' => get_permalink(),
            'poster' => get_the_post_thumbnail_url()
        );
    }
    
    wp_send_json_success($results);
}
```

## Security Measures

### Input Sanitization
```php
function sanitize_movie_data($data) {
    return array(
        'title' => sanitize_text_field($data['title']),
        'synopsis' => wp_kses_post($data['synopsis']),
        'year' => intval($data['year']),
        'rating' => floatval($data['rating']),
        'video_url' => esc_url_raw($data['video_url'])
    );
}
```

### User Permissions
```php
function check_movie_permissions() {
    if (!current_user_can('edit_posts')) {
        wp_die('Insufficient permissions');
    }
}
```

### Rate Limiting
```php
function check_rate_limit($action, $limit = 10) {
    $user_ip = $_SERVER['REMOTE_ADDR'];
    $transient_key = 'rate_limit_' . $action . '_' . md5($user_ip);
    $count = get_transient($transient_key);
    
    if ($count >= $limit) {
        wp_die('Rate limit exceeded');
    }
    
    set_transient($transient_key, $count + 1, HOUR_IN_SECONDS);
}
```

## Performance Optimization

### Caching Strategy
```php
// Object caching
function get_featured_movies() {
    $cache_key = 'featured_movies';
    $movies = wp_cache_get($cache_key);
    
    if (false === $movies) {
        $args = array(
            'post_type' => 'movie',
            'meta_key' => 'featured',
            'meta_value' => '1',
            'posts_per_page' => 10
        );
        $movies = new WP_Query($args);
        wp_cache_set($cache_key, $movies, '', 3600); // 1 hour
    }
    
    return $movies;
}
```

### Database Optimization
```php
// Optimize queries
function optimize_movie_queries() {
    // Add database indexes
    global $wpdb;
    $wpdb->query("ALTER TABLE {$wpdb->postmeta} ADD INDEX meta_key_value (meta_key, meta_value(10))");
}
```

### Image Optimization
```php
// Lazy loading and WebP support
function optimize_movie_images() {
    add_filter('wp_get_attachment_image_attributes', function($attr) {
        $attr['loading'] = 'lazy';
        return $attr;
    });
}
```

## Content Management

### Admin Interface Customization
```php
// Custom admin columns for movies
function add_movie_admin_columns($columns) {
    $columns['poster'] = 'Poster';
    $columns['imdb_rating'] = 'IMDB';
    $columns['year'] = 'Year';
    $columns['views'] = 'Views';
    return $columns;
}
add_filter('manage_movie_posts_columns', 'add_movie_admin_columns');
```

### Bulk Operations
```php
// Bulk import movies
function bulk_import_movies($csv_file) {
    $handle = fopen($csv_file, 'r');
    while (($data = fgetcsv($handle)) !== FALSE) {
        $movie_data = array(
            'post_title' => $data[0],
            'post_content' => $data[1],
            'post_type' => 'movie',
            'post_status' => 'publish'
        );
        
        $movie_id = wp_insert_post($movie_data);
        
        // Add meta data
        update_post_meta($movie_id, 'imdb_rating', $data[2]);
        update_post_meta($movie_id, 'release_year', $data[3]);
        // ... more meta fields
    }
    fclose($handle);
}
```
