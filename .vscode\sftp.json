{"name": "lnwmovie Server", "host": "*************", "protocol": "sftp", "port": 22, "username": "doomovieplus", "password": "EYsmrQ6(e-$Iik[,", "remotePath": "/home/<USER>/public_html/wp-content/themes/doomovie", "uploadOnSave": true, "useTempFile": false, "openSsh": false, "downloadOnOpen": false, "syncMode": "full", "watcher": {"files": "**/*", "autoUpload": true, "autoDelete": true}, "syncOption": {"delete": true, "skipCreate": false, "ignoreExisting": false, "update": true}, "ignore": ["**/.vscode/**", "**/.git/**", "**/node_modules/**", "**/.DS_Store", "**/Thumbs.db", "**/demo.html", "**/test-connection.txt"], "concurrency": 1, "connectTimeout": 30000, "interactiveAuth": false, "algorithms": {"kex": ["diffie-hellman-group1-sha1", "diffie-hellman-group14-sha1", "diffie-hellman-group-exchange-sha1", "diffie-hellman-group-exchange-sha256", "ecdh-sha2-nistp256", "ecdh-sha2-nistp384", "ecdh-sha2-nistp521"], "cipher": ["aes128-ctr", "aes192-ctr", "aes256-ctr", "aes128-gcm", "aes256-gcm", "aes128-cbc", "aes192-cbc", "aes256-cbc", "3des-cbc"], "serverHostKey": ["ssh-rsa", "ssh-dss", "ecdsa-sha2-nistp256", "ecdsa-sha2-nistp384", "ecdsa-sha2-nistp521"], "hmac": ["hmac-sha2-256", "hmac-sha2-512", "hmac-sha1", "hmac-md5"]}}