// Colors
:root {
  // Primary Colors
  --color-primary: #e50914;
  --color-primary-dark: #b20710;
  --color-primary-light: #f40612;
  
  // Background Colors
  --color-bg-primary: #000000;
  --color-bg-secondary: #141414;
  --color-bg-tertiary: #1a1a1a;
  --color-bg-card: #2a2a2a;
  --color-bg-overlay: rgba(0, 0, 0, 0.8);
  
  // Text Colors
  --color-text-primary: #ffffff;
  --color-text-secondary: #b3b3b3;
  --color-text-muted: #8c8c8c;
  --color-text-inverse: #000000;
  
  // Accent Colors
  --color-accent-gold: #ffd700;
  --color-accent-green: #46d369;
  --color-accent-blue: #0071eb;
  --color-accent-orange: #ff6b35;
  
  // Status Colors
  --color-success: #28a745;
  --color-warning: #ffc107;
  --color-error: #dc3545;
  --color-info: #17a2b8;
  
  // Border Colors
  --color-border: #333333;
  --color-border-light: #555555;
  --color-border-dark: #1a1a1a;
}

// Typography
:root {
  // Font Families
  --font-primary: 'Noto Sans Thai', 'Helvetica Neue', Arial, sans-serif;
  --font-secondary: 'Roboto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-mono: 'Fira Code', 'Courier New', monospace;
  
  // Font Sizes
  --font-size-xs: 0.75rem;    // 12px
  --font-size-sm: 0.875rem;   // 14px
  --font-size-base: 1rem;     // 16px
  --font-size-lg: 1.125rem;   // 18px
  --font-size-xl: 1.25rem;    // 20px
  --font-size-2xl: 1.5rem;    // 24px
  --font-size-3xl: 1.875rem;  // 30px
  --font-size-4xl: 2.25rem;   // 36px
  --font-size-5xl: 3rem;      // 48px
  
  // Font Weights
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  
  // Line Heights
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
}

// Spacing
:root {
  --spacing-xs: 0.25rem;   // 4px
  --spacing-sm: 0.5rem;    // 8px
  --spacing-md: 1rem;      // 16px
  --spacing-lg: 1.5rem;    // 24px
  --spacing-xl: 2rem;      // 32px
  --spacing-2xl: 3rem;     // 48px
  --spacing-3xl: 4rem;     // 64px
  --spacing-4xl: 6rem;     // 96px
}

// Breakpoints
:root {
  --breakpoint-xs: 0;
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-2xl: 1400px;
}

// Container
:root {
  --container-sm: 540px;
  --container-md: 720px;
  --container-lg: 960px;
  --container-xl: 1140px;
  --container-2xl: 1320px;
}

// Z-index
:root {
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

// Border Radius
:root {
  --border-radius-sm: 0.25rem;
  --border-radius-md: 0.375rem;
  --border-radius-lg: 0.5rem;
  --border-radius-xl: 0.75rem;
  --border-radius-2xl: 1rem;
  --border-radius-full: 9999px;
}

// Shadows
:root {
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

// Transitions
:root {
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 300ms ease-in-out;
  --transition-slow: 500ms ease-in-out;
}

// Grid
:root {
  --grid-columns: 12;
  --grid-gap: var(--spacing-md);
}

// Movie Card Specific
:root {
  --movie-card-width: 200px;
  --movie-card-height: 300px;
  --movie-card-aspect-ratio: 2/3;
}
