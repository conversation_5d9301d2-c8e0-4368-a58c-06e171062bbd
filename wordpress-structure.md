# โครงสร้างไฟล์ WordPress Template - DoMovie Theme

## โครงสร้างโฟลเดอร์หลัก

```
doomovie/
├── wordpress/
│   └── wp-content/
│       └── themes/
│           └── doomovie-theme/
│               ├── style.css
│               ├── functions.php
│               ├── index.php
│               ├── header.php
│               ├── footer.php
│               ├── sidebar.php
│               ├── 404.php
│               ├── search.php
│               ├── searchform.php
│               ├── comments.php
│               ├── single.php
│               ├── single-movie.php
│               ├── archive.php
│               ├── archive-movie.php
│               ├── page.php
│               ├── page-categories.php
│               ├── taxonomy.php
│               ├── taxonomy-movie_genre.php
│               ├── taxonomy-movie_country.php
│               ├── taxonomy-movie_year.php
│               ├── screenshot.png
│               ├── assets/
│               │   ├── css/
│               │   │   ├── main.css
│               │   │   ├── responsive.css
│               │   │   └── admin.css
│               │   ├── js/
│               │   │   ├── main.js
│               │   │   ├── movie-player.js
│               │   │   ├── search.js
│               │   │   ├── modal.js
│               │   │   └── admin.js
│               │   ├── images/
│               │   │   ├── logo/
│               │   │   ├── icons/
│               │   │   ├── placeholders/
│               │   │   └── ui/
│               │   └── fonts/
│               ├── inc/
│               │   ├── custom-post-types.php
│               │   ├── custom-taxonomies.php
│               │   ├── custom-fields.php
│               │   ├── enqueue-scripts.php
│               │   ├── ajax-handlers.php
│               │   ├── theme-support.php
│               │   ├── customizer.php
│               │   ├── admin-functions.php
│               │   ├── user-functions.php
│               │   └── security.php
│               ├── template-parts/
│               │   ├── header/
│               │   │   ├── navigation.php
│               │   │   ├── hero-slider.php
│               │   │   └── search-form.php
│               │   ├── content/
│               │   │   ├── movie-card.php
│               │   │   ├── movie-grid.php
│               │   │   ├── movie-list.php
│               │   │   ├── movie-detail.php
│               │   │   ├── featured-movies.php
│               │   │   └── top-movies.php
│               │   ├── footer/
│               │   │   ├── footer-widgets.php
│               │   │   ├── footer-features.php
│               │   │   ├── footer-faq.php
│               │   │   └── footer-sponsors.php
│               │   └── modals/
│               │       ├── login-modal.php
│               │       ├── register-modal.php
│               │       ├── video-modal.php
│               │       └── report-modal.php
│               ├── woocommerce/
│               │   └── (ถ้ามี premium features)
│               └── languages/
│                   ├── th.po
│                   ├── th.mo
│                   └── doomovie-theme.pot
├── plugins/
│   └── doomovie-core/
│       ├── doomovie-core.php
│       ├── includes/
│       │   ├── class-movie-manager.php
│       │   ├── class-user-manager.php
│       │   ├── class-ajax-handler.php
│       │   ├── class-api-endpoints.php
│       │   ├── class-video-player.php
│       │   └── class-admin-panel.php
│       ├── admin/
│       │   ├── admin-menu.php
│       │   ├── movie-meta-boxes.php
│       │   ├── bulk-operations.php
│       │   └── settings-page.php
│       ├── public/
│       │   ├── css/
│       │   ├── js/
│       │   └── images/
│       └── languages/
├── src/
│   ├── scss/
│   │   ├── abstracts/
│   │   │   ├── _variables.scss
│   │   │   ├── _mixins.scss
│   │   │   ├── _functions.scss
│   │   │   └── _breakpoints.scss
│   │   ├── base/
│   │   │   ├── _reset.scss
│   │   │   ├── _typography.scss
│   │   │   ├── _utilities.scss
│   │   │   └── _animations.scss
│   │   ├── components/
│   │   │   ├── _buttons.scss
│   │   │   ├── _cards.scss
│   │   │   ├── _modals.scss
│   │   │   ├── _forms.scss
│   │   │   ├── _navigation.scss
│   │   │   ├── _slider.scss
│   │   │   └── _video-player.scss
│   │   ├── layout/
│   │   │   ├── _header.scss
│   │   │   ├── _footer.scss
│   │   │   ├── _sidebar.scss
│   │   │   ├── _grid.scss
│   │   │   └── _container.scss
│   │   ├── pages/
│   │   │   ├── _home.scss
│   │   │   ├── _movie-detail.scss
│   │   │   ├── _categories.scss
│   │   │   ├── _search.scss
│   │   │   └── _404.scss
│   │   ├── themes/
│   │   │   ├── _dark.scss
│   │   │   └── _light.scss
│   │   └── main.scss
│   ├── js/
│   │   ├── components/
│   │   │   ├── MovieCard.js
│   │   │   ├── VideoPlayer.js
│   │   │   ├── SearchBox.js
│   │   │   ├── Modal.js
│   │   │   ├── Slider.js
│   │   │   ├── LazyLoad.js
│   │   │   └── Navigation.js
│   │   ├── utils/
│   │   │   ├── api.js
│   │   │   ├── helpers.js
│   │   │   ├── constants.js
│   │   │   ├── storage.js
│   │   │   └── validation.js
│   │   ├── pages/
│   │   │   ├── home.js
│   │   │   ├── movie-detail.js
│   │   │   ├── categories.js
│   │   │   └── search.js
│   │   └── main.js
│   └── images/
│       ├── logos/
│       ├── icons/
│       ├── placeholders/
│       ├── ui/
│       └── backgrounds/
├── dist/
│   ├── css/
│   ├── js/
│   └── images/
├── docs/
│   ├── installation.md
│   ├── customization.md
│   ├── api-documentation.md
│   └── troubleshooting.md
└── tools/
    ├── webpack.config.js
    ├── gulpfile.js
    ├── package.json
    └── composer.json
```

## รายละเอียดไฟล์สำคัญ

### 1. Theme Files หลัก

#### style.css
```css
/*
Theme Name: DoMovie Theme
Description: EZMovie.me inspired WordPress theme for movie streaming websites
Author: Your Name
Version: 1.0.0
License: GPL v2 or later
Text Domain: doomovie-theme
Domain Path: /languages
*/
```

#### functions.php
- Theme setup และ support
- Enqueue scripts และ styles
- Include ไฟล์ใน inc/
- Custom functions

#### index.php
- Template หลักสำหรับหน้าแรก
- แสดง featured movies
- Movie sections ต่างๆ

### 2. Template Files

#### single-movie.php
- หน้ารายละเอียดหนัง
- Video player
- Movie information
- Related movies

#### archive-movie.php
- หน้าแสดงรายการหนัง
- Grid layout
- Pagination
- Filter options

#### page-categories.php
- หน้าแสดงหมวดหมู่ทั้งหมด
- Category grid
- Search functionality

#### taxonomy-movie_genre.php
- หน้าแสดงหนังตามประเภท
- Filter และ sort options

### 3. Template Parts

#### template-parts/content/movie-card.php
```php
<div class="movie-card">
    <div class="movie-poster">
        <img src="<?php echo get_movie_poster(); ?>" alt="<?php the_title(); ?>" loading="lazy">
        <div class="movie-overlay">
            <a href="<?php the_permalink(); ?>" class="play-button">ดูหนัง</a>
            <button class="add-to-watchlist" data-movie-id="<?php the_ID(); ?>">รายการของฉัน</button>
        </div>
    </div>
    <div class="movie-info">
        <h3 class="movie-title"><?php the_title(); ?></h3>
        <div class="movie-meta">
            <span class="year"><?php echo get_movie_year(); ?></span>
            <span class="rating"><?php echo get_movie_rating(); ?></span>
            <span class="duration"><?php echo get_movie_duration(); ?></span>
        </div>
        <div class="movie-genres">
            <?php echo get_movie_genres(); ?>
        </div>
    </div>
</div>
```

#### template-parts/header/hero-slider.php
- Swiper.js slider
- Featured movies
- Auto-play functionality

#### template-parts/modals/login-modal.php
- Login form
- Register form
- Password reset

### 4. Include Files (inc/)

#### inc/custom-post-types.php
```php
function register_movie_post_type() {
    register_post_type('movie', array(
        'labels' => array(
            'name' => 'Movies',
            'singular_name' => 'Movie'
        ),
        'public' => true,
        'has_archive' => true,
        'supports' => array('title', 'editor', 'thumbnail', 'custom-fields'),
        'menu_icon' => 'dashicons-video-alt3',
        'rewrite' => array('slug' => 'movie')
    ));
}
```

#### inc/custom-taxonomies.php
- Movie genres
- Countries
- Years
- Quality levels

#### inc/ajax-handlers.php
- Search functionality
- Watchlist management
- Problem reporting
- User authentication

### 5. Assets Structure

#### assets/css/main.css
- Compiled SCSS
- Dark theme styling
- Responsive design
- Component styles

#### assets/js/main.js
- Bundled JavaScript
- Component initialization
- Event handlers
- API calls

### 6. Plugin Structure (doomovie-core/)

#### doomovie-core.php
```php
<?php
/**
 * Plugin Name: DoMovie Core
 * Description: Core functionality for DoMovie theme
 * Version: 1.0.0
 * Author: Your Name
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('DOOMOVIE_CORE_VERSION', '1.0.0');
define('DOOMOVIE_CORE_PATH', plugin_dir_path(__FILE__));
define('DOOMOVIE_CORE_URL', plugin_dir_url(__FILE__));

// Include required files
require_once DOOMOVIE_CORE_PATH . 'includes/class-movie-manager.php';
require_once DOOMOVIE_CORE_PATH . 'includes/class-ajax-handler.php';
require_once DOOMOVIE_CORE_PATH . 'includes/class-api-endpoints.php';

// Initialize plugin
function doomovie_core_init() {
    new DoMovie_Core();
}
add_action('plugins_loaded', 'doomovie_core_init');
```

## การติดตั้งและใช้งาน

### 1. Requirements
- WordPress 5.0+
- PHP 7.4+
- MySQL 5.7+

### 2. Installation Steps
1. อัปโหลด theme ไปยัง `/wp-content/themes/`
2. อัปโหลด plugin ไปยัง `/wp-content/plugins/`
3. เปิดใช้งาน theme และ plugin
4. Import demo content (ถ้ามี)
5. ตั้งค่า permalinks
6. กำหนดค่า theme options

### 3. Customization
- Theme Customizer
- Widget areas
- Menu locations
- Custom CSS
- Child theme support

## Features ที่รองรับ

### 1. WordPress Features
- Custom Post Types
- Custom Taxonomies
- Custom Fields
- Widget Areas
- Menu Locations
- Theme Customizer
- Responsive Design

### 2. Movie Features
- Video Player Integration
- Multiple Audio Tracks
- Quality Selection
- Watchlist Management
- Rating System
- Search & Filter
- Related Movies

### 3. User Features
- User Registration/Login
- Watchlist Management
- Movie Requests
- Problem Reporting
- Social Sharing

### 4. Admin Features
- Movie Management
- Bulk Operations
- Analytics Dashboard
- User Management
- Content Moderation

## Performance Optimization

### 1. Caching
- Object caching
- Page caching
- Database query optimization
- Image optimization

### 2. CDN Integration
- Image CDN support
- Asset optimization
- Lazy loading
- Progressive loading

### 3. SEO Optimization
- Schema markup
- Meta tags
- Sitemap generation
- Social media integration

---
*โครงสร้างนี้ออกแบบให้รองรับการพัฒนา WordPress theme ที่สมบูรณ์และปรับขนาดได้*
