const axios = require('axios');
const fs = require('fs-extra');
const path = require('path');
const { URL } = require('url');

class AssetDownloader {
    constructor() {
        this.baseUrl = 'https://ezmovie.me';
        this.outputDir = './downloaded-assets';
        this.analysisDir = './research/detailed-analysis';
        this.downloadedCount = 0;
        this.failedCount = 0;
    }

    async init() {
        console.log('📥 เริ่มต้นการดาวน์โหลด Assets...');
        
        // สร้างโฟลเดอร์สำหรับเก็บ assets
        await fs.ensureDir(this.outputDir);
        await fs.ensureDir(path.join(this.outputDir, 'css'));
        await fs.ensureDir(path.join(this.outputDir, 'js'));
        await fs.ensureDir(path.join(this.outputDir, 'images'));
        await fs.ensureDir(path.join(this.outputDir, 'fonts'));
    }

    async downloadFile(url, outputPath, retries = 3) {
        try {
            console.log(`⬇️ กำลังดาวน์โหลด: ${url}`);
            
            const response = await axios({
                method: 'GET',
                url: url,
                responseType: 'stream',
                timeout: 30000,
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                }
            });

            // สร้างโฟลเดอร์ถ้ายังไม่มี
            await fs.ensureDir(path.dirname(outputPath));

            // เขียนไฟล์
            const writer = fs.createWriteStream(outputPath);
            response.data.pipe(writer);

            return new Promise((resolve, reject) => {
                writer.on('finish', () => {
                    this.downloadedCount++;
                    console.log(`✅ ดาวน์โหลดสำเร็จ: ${path.basename(outputPath)}`);
                    resolve();
                });
                writer.on('error', reject);
            });

        } catch (error) {
            if (retries > 0) {
                console.log(`⚠️ ลองใหม่ (${retries} ครั้งที่เหลือ): ${url}`);
                await new Promise(resolve => setTimeout(resolve, 1000));
                return this.downloadFile(url, outputPath, retries - 1);
            } else {
                this.failedCount++;
                console.error(`❌ ดาวน์โหลดไม่สำเร็จ: ${url} - ${error.message}`);
                throw error;
            }
        }
    }

    getFileExtension(url) {
        try {
            const urlObj = new URL(url);
            const pathname = urlObj.pathname;
            const ext = path.extname(pathname);
            return ext || '.unknown';
        } catch {
            return '.unknown';
        }
    }

    sanitizeFilename(filename) {
        return filename.replace(/[^a-zA-Z0-9.-]/g, '_');
    }

    async downloadFromAnalysis() {
        console.log('📊 อ่านข้อมูลจากการวิเคราะห์...');
        
        try {
            const homepageAnalysisPath = path.join(this.analysisDir, 'homepage-analysis.json');
            
            if (!(await fs.pathExists(homepageAnalysisPath))) {
                console.log('⚠️ ไม่พบไฟล์การวิเคราะห์ กรุณารันคำสั่ง npm run analyze ก่อน');
                return;
            }

            const analysisData = await fs.readJSON(homepageAnalysisPath);
            
            // ดาวน์โหลด CSS files
            if (analysisData.assets && analysisData.assets.css) {
                console.log(`🎨 ดาวน์โหลด CSS files (${analysisData.assets.css.length} ไฟล์)...`);
                
                for (const [index, cssFile] of analysisData.assets.css.entries()) {
                    try {
                        const url = cssFile.href;
                        const filename = this.sanitizeFilename(`style_${index}${this.getFileExtension(url)}`);
                        const outputPath = path.join(this.outputDir, 'css', filename);
                        
                        await this.downloadFile(url, outputPath);
                        
                        // บันทึกข้อมูล metadata
                        const metadata = {
                            originalUrl: url,
                            media: cssFile.media,
                            downloadedAt: new Date().toISOString()
                        };
                        await fs.writeJSON(outputPath + '.meta.json', metadata, { spaces: 2 });
                        
                    } catch (error) {
                        console.error(`❌ ไม่สามารถดาวน์โหลด CSS: ${cssFile.href}`);
                    }
                }
            }

            // ดาวน์โหลด JavaScript files
            if (analysisData.assets && analysisData.assets.js) {
                console.log(`⚡ ดาวน์โหลด JavaScript files (${analysisData.assets.js.length} ไฟล์)...`);
                
                for (const [index, jsFile] of analysisData.assets.js.entries()) {
                    try {
                        const url = jsFile.src;
                        const filename = this.sanitizeFilename(`script_${index}${this.getFileExtension(url)}`);
                        const outputPath = path.join(this.outputDir, 'js', filename);
                        
                        await this.downloadFile(url, outputPath);
                        
                        // บันทึกข้อมูล metadata
                        const metadata = {
                            originalUrl: url,
                            async: jsFile.async,
                            defer: jsFile.defer,
                            downloadedAt: new Date().toISOString()
                        };
                        await fs.writeJSON(outputPath + '.meta.json', metadata, { spaces: 2 });
                        
                    } catch (error) {
                        console.error(`❌ ไม่สามารถดาวน์โหลด JS: ${jsFile.src}`);
                    }
                }
            }

            // ดาวน์โหลดรูปภาพตัวอย่าง (จำกัดจำนวน)
            if (analysisData.assets && analysisData.assets.images) {
                const imagesToDownload = analysisData.assets.images.slice(0, 20); // จำกัดแค่ 20 รูป
                console.log(`🖼️ ดาวน์โหลดรูปภาพตัวอย่าง (${imagesToDownload.length} ไฟล์)...`);
                
                for (const [index, imgFile] of imagesToDownload.entries()) {
                    try {
                        const url = imgFile.src;
                        const ext = this.getFileExtension(url);
                        const filename = this.sanitizeFilename(`image_${index}${ext}`);
                        const outputPath = path.join(this.outputDir, 'images', filename);
                        
                        await this.downloadFile(url, outputPath);
                        
                        // บันทึกข้อมูล metadata
                        const metadata = {
                            originalUrl: url,
                            alt: imgFile.alt,
                            loading: imgFile.loading,
                            className: imgFile.className,
                            downloadedAt: new Date().toISOString()
                        };
                        await fs.writeJSON(outputPath + '.meta.json', metadata, { spaces: 2 });
                        
                    } catch (error) {
                        console.error(`❌ ไม่สามารถดาวน์โหลดรูปภาพ: ${imgFile.src}`);
                    }
                }
            }

        } catch (error) {
            console.error('❌ เกิดข้อผิดพลาดในการอ่านข้อมูลการวิเคราะห์:', error.message);
        }
    }

    async downloadSpecificAssets() {
        console.log('🎯 ดาวน์โหลด Assets เฉพาะที่สำคัญ...');
        
        // รายการ assets ที่สำคัญที่ต้องการดาวน์โหลด
        const importantAssets = [
            {
                url: 'https://ezmovie.me/build/web/ez-movie/css/main.css',
                type: 'css',
                filename: 'main.css'
            },
            {
                url: 'https://ezmovie.me/build/web/ez-movie/js/main.js',
                type: 'js',
                filename: 'main.js'
            },
            {
                url: 'https://ezmovie.me/build/web/ez-movie/img/ez-movie-logo.png',
                type: 'images',
                filename: 'logo.png'
            },
            {
                url: 'https://ezmovie.me/build/web/ez-movie/img/movie-loadding.gif',
                type: 'images',
                filename: 'loading.gif'
            }
        ];

        for (const asset of importantAssets) {
            try {
                const outputPath = path.join(this.outputDir, asset.type, asset.filename);
                await this.downloadFile(asset.url, outputPath);
            } catch (error) {
                console.log(`⚠️ ไม่สามารถดาวน์โหลด ${asset.filename} (อาจไม่มีไฟล์นี้)`);
            }
        }
    }

    async generateAssetReport() {
        console.log('📋 สร้างรายงาน Assets...');
        
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                totalDownloaded: this.downloadedCount,
                totalFailed: this.failedCount,
                successRate: this.downloadedCount / (this.downloadedCount + this.failedCount) * 100
            },
            directories: {}
        };

        // สแกนโฟลเดอร์ที่ดาวน์โหลด
        const assetTypes = ['css', 'js', 'images', 'fonts'];
        
        for (const type of assetTypes) {
            const dirPath = path.join(this.outputDir, type);
            if (await fs.pathExists(dirPath)) {
                const files = await fs.readdir(dirPath);
                const assetFiles = files.filter(file => !file.endsWith('.meta.json'));
                
                report.directories[type] = {
                    count: assetFiles.length,
                    files: assetFiles
                };
            }
        }

        await fs.writeJSON(path.join(this.outputDir, 'download-report.json'), report, { spaces: 2 });
        
        // สร้างรายงาน Markdown
        const markdown = `# รายงานการดาวน์โหลด Assets

## สรุป
- **ดาวน์โหลดสำเร็จ**: ${report.summary.totalDownloaded} ไฟล์
- **ดาวน์โหลดไม่สำเร็จ**: ${report.summary.totalFailed} ไฟล์
- **อัตราความสำเร็จ**: ${report.summary.successRate.toFixed(2)}%

## รายละเอียดไฟล์

${assetTypes.map(type => {
    const data = report.directories[type];
    if (data) {
        return `### ${type.toUpperCase()}
- **จำนวน**: ${data.count} ไฟล์
- **รายชื่อไฟล์**: ${data.files.join(', ')}`;
    }
    return `### ${type.toUpperCase()}
- ไม่มีไฟล์`;
}).join('\n\n')}

---
*รายงานสร้างเมื่อ: ${new Date(report.timestamp).toLocaleString('th-TH')}*
`;

        await fs.writeFile(path.join(this.outputDir, 'download-report.md'), markdown);
        
        console.log('✅ สร้างรายงาน Assets เสร็จสิ้น');
    }

    async run() {
        try {
            await this.init();
            
            // ดาวน์โหลดจากข้อมูลการวิเคราะห์
            await this.downloadFromAnalysis();
            
            // ดาวน์โหลด assets เฉพาะที่สำคัญ
            await this.downloadSpecificAssets();
            
            // สร้างรายงาน
            await this.generateAssetReport();
            
            console.log('🎉 การดาวน์โหลด Assets เสร็จสิ้นแล้ว!');
            console.log(`📁 ไฟล์อยู่ในโฟลเดอร์: ${this.outputDir}`);
            console.log(`📊 ดาวน์โหลดสำเร็จ: ${this.downloadedCount} ไฟล์`);
            console.log(`❌ ดาวน์โหลดไม่สำเร็จ: ${this.failedCount} ไฟล์`);
            
        } catch (error) {
            console.error('❌ เกิดข้อผิดพลาด:', error.message);
        }
    }
}

// รันการดาวน์โหลด
if (require.main === module) {
    const downloader = new AssetDownloader();
    downloader.run();
}

module.exports = AssetDownloader;
