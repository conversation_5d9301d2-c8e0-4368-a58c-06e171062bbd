/* Header */

.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: var(--color-bg-primary);
  border-bottom: 1px solid var(--color-border);
  z-index: var(--z-fixed);
  transition: all var(--transition-fast);
  
  &.scrolled {
    background-color: var(--color-bg-overlay);
    @include backdrop-blur(10px);
  }
}

/* Navbar */
.navbar {
  @include flex-between;
  padding: var(--spacing-md) 0;
  
  @include respond-to(lg) {
    padding: var(--spacing-lg) 0;
  }
}

.navbar__brand {
  flex-shrink: 0;
}

.navbar__menu {
  @include flex-center;
  gap: var(--spacing-lg);
  
  @media (max-width: 991px) {
    display: none;
  }
}

.navbar__item {
  position: relative;
}

.navbar__link {
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
  transition: color var(--transition-fast);
  border-radius: var(--border-radius-md);
  
  &:hover,
  &--active {
    color: var(--color-text-primary);
    background-color: var(--color-bg-tertiary);
  }
  
  &--active {
    color: var(--color-primary);
  }
}

.navbar__actions {
  @include flex-center;
  gap: var(--spacing-md);
}

/* Logo */
.logo {
  @include flex-center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  text-decoration: none;
}

.logo__image {
  width: 40px;
  height: 40px;
  object-fit: contain;
}

.logo__text {
  color: var(--color-primary);
}

/* Search Bar */
.search-bar {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--color-bg-secondary);
  border-bottom: 1px solid var(--color-border);
  padding: var(--spacing-md) 0;
  transform: translateY(-100%);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
  
  &.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }
}

.search-bar__container {
  position: relative;
  max-width: 600px;
  margin: 0 auto;
}

.search-bar__input {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-xl) var(--spacing-md) var(--spacing-md);
  background-color: var(--color-bg-tertiary);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-lg);
  color: var(--color-text-primary);
  font-size: var(--font-size-base);
  
  &::placeholder {
    color: var(--color-text-muted);
  }
  
  &:focus {
    border-color: var(--color-primary);
    outline: none;
  }
}

.search-bar__submit,
.search-bar__close {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--border-radius-md);
  transition: all var(--transition-fast);
  
  &:hover {
    color: var(--color-text-primary);
    background-color: var(--color-bg-card);
  }
}

.search-bar__submit {
  right: var(--spacing-xl);
}

.search-bar__close {
  right: var(--spacing-sm);
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--color-bg-tertiary);
  border: 1px solid var(--color-border);
  border-top: none;
  border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
  max-height: 400px;
  overflow-y: auto;
  z-index: 10;
  
  @include custom-scrollbar;
  
  &:empty {
    display: none;
  }
}

/* Search Toggle Button */
.search-toggle {
  @include button-secondary;
  padding: var(--spacing-sm);
  border-radius: var(--border-radius-md);
}

/* Login Button */
.login-btn {
  @include button-primary;
  font-size: var(--font-size-sm);
  
  @media (max-width: 767px) {
    display: none;
  }
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  gap: 4px;
  padding: var(--spacing-sm);
  background: none;
  border: none;
  cursor: pointer;
  
  @media (max-width: 991px) {
    display: flex;
  }
  
  span {
    width: 20px;
    height: 2px;
    background-color: var(--color-text-primary);
    transition: all var(--transition-fast);
    border-radius: 1px;
  }
  
  &.active {
    span:nth-child(1) {
      transform: rotate(45deg) translate(5px, 5px);
    }
    
    span:nth-child(2) {
      opacity: 0;
    }
    
    span:nth-child(3) {
      transform: rotate(-45deg) translate(7px, -6px);
    }
  }
}

/* Mobile Navigation */
.mobile-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--color-bg-secondary);
  border-top: 1px solid var(--color-border);
  display: flex;
  justify-content: space-around;
  padding: var(--spacing-sm) 0;
  z-index: var(--z-fixed);
  
  @include respond-to(lg) {
    display: none;
  }
}

.mobile-nav__item {
  @include flex-column;
  align-items: center;
  gap: 4px;
  padding: var(--spacing-xs) var(--spacing-sm);
  color: var(--color-text-muted);
  text-decoration: none;
  font-size: var(--font-size-xs);
  border-radius: var(--border-radius-md);
  transition: all var(--transition-fast);
  background: none;
  border: none;
  cursor: pointer;
  
  &:hover,
  &--active {
    color: var(--color-primary);
    background-color: var(--color-bg-tertiary);
  }
  
  .icon {
    width: 20px;
    height: 20px;
  }
}
