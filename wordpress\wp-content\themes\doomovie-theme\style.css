/*
Theme Name: DoMovie Theme
Description: EZMovie.me inspired WordPress theme for movie streaming websites. Features dark theme, responsive design, and comprehensive movie management system.
Author: DoMovie Team
Version: 1.0.0
License: GPL v2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html
Text Domain: doomovie-theme
Domain Path: /languages
Tags: dark, movies, streaming, responsive, custom-post-types, entertainment
Requires at least: 5.0
Tested up to: 6.4
Requires PHP: 7.4

DoMovie Theme, Copyright 2025
DoMovie Theme is distributed under the terms of the GNU GPL.

This program is free software: you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation, either version 2 of the License, or
(at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
GNU General Public License for more details.
*/

/* ==========================================================================
   CSS Reset & Base Styles
   ========================================================================== */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Noto Sans Thai', 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #ffffff;
    background-color: #0a0a0a;
    overflow-x: hidden;
}

/* ==========================================================================
   CSS Variables (Dark Theme)
   ========================================================================== */

:root {
    /* Primary Colors */
    --primary-black: #000000;
    --primary-red: #e50914;
    --primary-gold: #ffd700;
    
    /* Background Colors */
    --bg-dark: #141414;
    --bg-darker: #0a0a0a;
    --bg-card: #1a1a1a;
    --bg-overlay: rgba(0, 0, 0, 0.8);
    
    /* Text Colors */
    --text-white: #ffffff;
    --text-gray: #b3b3b3;
    --text-light-gray: #8c8c8c;
    --text-dark-gray: #666666;
    
    /* Accent Colors */
    --accent-red: #e50914;
    --accent-hover: #f40612;
    --accent-gold: #ffd700;
    
    /* Border Colors */
    --border-gray: #333333;
    --border-light: #555555;
    
    /* Font Sizes */
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-base: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 24px;
    --font-size-2xl: 32px;
    --font-size-3xl: 48px;
    
    /* Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-2xl: 48px;
    
    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    
    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.5);
    
    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Z-index */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

/* ==========================================================================
   Typography
   ========================================================================== */

h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
    color: var(--text-white);
}

h1 { font-size: var(--font-size-3xl); }
h2 { font-size: var(--font-size-2xl); }
h3 { font-size: var(--font-size-xl); }
h4 { font-size: var(--font-size-lg); }
h5 { font-size: var(--font-size-base); }
h6 { font-size: var(--font-size-sm); }

p {
    margin-bottom: var(--spacing-md);
    color: var(--text-gray);
}

a {
    color: var(--accent-red);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--accent-hover);
}

/* ==========================================================================
   Layout & Container
   ========================================================================== */

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.container-fluid {
    width: 100%;
    padding: 0 var(--spacing-md);
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 calc(var(--spacing-md) * -0.5);
}

.col {
    flex: 1;
    padding: 0 calc(var(--spacing-md) * 0.5);
}

/* ==========================================================================
   Grid System
   ========================================================================== */

.movie-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    margin: var(--spacing-xl) 0;
}

@media (max-width: 576px) {
    .movie-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }
}

@media (min-width: 577px) and (max-width: 768px) {
    .movie-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (min-width: 769px) and (max-width: 992px) {
    .movie-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (min-width: 993px) and (max-width: 1200px) {
    .movie-grid {
        grid-template-columns: repeat(5, 1fr);
    }
}

@media (min-width: 1201px) {
    .movie-grid {
        grid-template-columns: repeat(6, 1fr);
    }
}

/* ==========================================================================
   Buttons
   ========================================================================== */

.btn {
    display: inline-block;
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--font-size-base);
    font-weight: 600;
    text-align: center;
    text-decoration: none;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    line-height: 1.5;
}

.btn-primary {
    background-color: var(--accent-red);
    color: var(--text-white);
}

.btn-primary:hover {
    background-color: var(--accent-hover);
    color: var(--text-white);
}

.btn-secondary {
    background-color: transparent;
    color: var(--text-white);
    border: 2px solid var(--border-gray);
}

.btn-secondary:hover {
    background-color: var(--text-white);
    color: var(--bg-dark);
    border-color: var(--text-white);
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-sm);
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-lg);
}

/* ==========================================================================
   Forms
   ========================================================================== */

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 600;
    color: var(--text-white);
}

.form-control {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-base);
    color: var(--text-white);
    background-color: var(--bg-card);
    border: 2px solid var(--border-gray);
    border-radius: var(--radius-md);
    transition: border-color var(--transition-fast);
}

.form-control:focus {
    outline: none;
    border-color: var(--accent-red);
}

.form-control::placeholder {
    color: var(--text-dark-gray);
}

/* ==========================================================================
   Utilities
   ========================================================================== */

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-none { display: none; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.justify-content-around { justify-content: space-around; }

.align-items-center { align-items: center; }
.align-items-start { align-items: flex-start; }
.align-items-end { align-items: flex-end; }

.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.w-100 { width: 100%; }
.h-100 { height: 100%; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

/* ==========================================================================
   Responsive Utilities
   ========================================================================== */

@media (max-width: 576px) {
    .d-sm-none { display: none; }
    .d-sm-block { display: block; }
    .text-sm-center { text-align: center; }
}

@media (max-width: 768px) {
    .d-md-none { display: none; }
    .d-md-block { display: block; }
    .text-md-center { text-align: center; }
}

@media (max-width: 992px) {
    .d-lg-none { display: none; }
    .d-lg-block { display: block; }
    .text-lg-center { text-align: center; }
}

/* ==========================================================================
   Loading & Animations
   ========================================================================== */

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.fade-in {
    animation: fadeIn var(--transition-normal);
}

.slide-in-up {
    animation: slideInUp var(--transition-normal);
}

.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* ==========================================================================
   Print Styles
   ========================================================================== */

@media print {
    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }
    
    a, a:visited {
        text-decoration: underline;
    }
    
    .no-print {
        display: none !important;
    }
}
