# DoMovie Template

เทมเพลตเว็บไซต์ดูหนังออนไลน์ที่ได้แรงบันดาลใจจาก EZMovie.me

## ข้อกำหนดระบบ

### ซอฟต์แวร์ที่จำเป็น
1. **Node.js** (เวอร์ชัน 16 หรือใหม่กว่า)
   - ดาวน์โหลดจาก: https://nodejs.org/
   - เลือก LTS version

2. **Git** (สำหรับ version control)
   - ดาวน์โหลดจาก: https://git-scm.com/

3. **Code Editor** (แนะนำ)
   - Visual Studio Code: https://code.visualstudio.com/
   - หรือ editor อื่นๆ ที่ถนัด

## การติดตั้ง

### 1. ติดตั้ง Node.js
1. ไปที่ https://nodejs.org/
2. ดาวน์โหลด LTS version
3. ติดตั้งตามขั้นตอน
4. เปิด Command Prompt หรือ PowerShell ใหม่
5. ตรวจสอบการติดตั้ง:
   ```bash
   node --version
   npm --version
   ```

### 2. ติดตั้ง Dependencies
```bash
# ติดตั้ง packages ที่จำเป็น
npm install

# หรือใช้ yarn (ถ้าติดตั้งแล้ว)
yarn install
```

### 3. เริ่มต้นการพัฒนา
```bash
# Development mode (hot reload)
npm run dev

# หรือ
npm start
```

### 4. Build สำหรับ Production
```bash
# Build files
npm run build

# Build CSS เท่านั้น
npm run sass-build
```

## โครงสร้างโปรเจค

```
doomovie/
├── src/                    # Source files
│   ├── scss/              # Sass/SCSS files
│   │   ├── abstracts/     # Variables, mixins
│   │   ├── base/          # Reset, typography
│   │   ├── components/    # UI components
│   │   ├── layout/        # Layout components
│   │   ├── pages/         # Page-specific styles
│   │   └── utilities/     # Utility classes
│   ├── js/                # JavaScript files
│   │   ├── components/    # JS components
│   │   ├── utils/         # Utility functions
│   │   └── main.js        # Main JS file
│   ├── images/            # Images and assets
│   └── *.html             # HTML templates
├── dist/                  # Built files (generated)
├── wordpress/             # WordPress theme files
├── research/              # Research and analysis
├── package.json           # NPM configuration
├── webpack.config.js      # Webpack configuration
└── README.md             # This file
```

## ฟีเจอร์หลัก

### ✅ ที่พัฒนาแล้ว
- [x] โครงสร้างโปรเจคพื้นฐาน
- [x] Webpack configuration
- [x] Sass/SCSS setup
- [x] HTML templates
- [x] CSS variables และ mixins
- [x] Responsive design foundation
- [x] **Standalone CSS และ JavaScript** (ใช้งานได้ทันทีโดยไม่ต้อง build)
- [x] **Hero slider component**
- [x] **Movie card component**
- [x] **Search functionality (พื้นฐาน)**
- [x] **Mobile navigation**
- [x] **Responsive header และ footer**

### 🚧 กำลังพัฒนา
- [ ] Video player integration
- [ ] Modal system
- [ ] Advanced search features
- [ ] User authentication
- [ ] Watchlist functionality

### 📋 แผนการพัฒนา
- [ ] WordPress theme integration
- [ ] Backend API
- [ ] User authentication
- [ ] Admin panel
- [ ] Performance optimization

## การใช้งาน

### 🚀 Quick Start (ไม่ต้องติดตั้ง Node.js)
1. เปิดไฟล์ `index.html` ในเบราว์เซอร์
2. เว็บไซต์พร้อมใช้งานทันที!

### Development (ต้องติดตั้ง Node.js)
1. เปิด terminal ในโฟลเดอร์โปรเจค
2. รัน `npm install` (ครั้งแรกเท่านั้น)
3. รัน `npm run dev`
4. เปิดเบราว์เซอร์ไปที่ `http://localhost:3000`
5. แก้ไขไฟล์ในโฟลเดอร์ `src/`
6. เบราว์เซอร์จะ reload อัตโนมัติ

### Production Build
1. รัน `npm run build`
2. ไฟล์ที่ build แล้วจะอยู่ในโฟลเดอร์ `dist/`
3. Upload ไฟล์ในโฟลเดอร์ `dist/` ไปยัง web server

### 📁 ไฟล์สำคัญ
- `index.html` - หน้าเว็บหลัก (พร้อมใช้งาน)
- `dist/css/style.css` - CSS ที่รวมทุกอย่างแล้ว
- `dist/js/main.js` - JavaScript ที่รวมทุกอย่างแล้ว

## การปรับแต่ง

### สี (Colors)
แก้ไขไฟล์ `src/scss/abstracts/_variables.scss`:
```scss
:root {
  --color-primary: #e50914;        // สีหลัก
  --color-bg-primary: #000000;     // สีพื้นหลัง
  --color-text-primary: #ffffff;   // สีข้อความ
}
```

### Fonts
แก้ไขไฟล์ `src/scss/abstracts/_variables.scss`:
```scss
:root {
  --font-primary: 'Noto Sans Thai', Arial, sans-serif;
}
```

### Breakpoints
แก้ไขไฟล์ `src/scss/abstracts/_mixins.scss`:
```scss
@mixin respond-to($breakpoint) {
  @if $breakpoint == sm {
    @media (min-width: 576px) { @content; }
  }
  // เพิ่ม breakpoints อื่นๆ
}
```

## การแก้ไขปัญหา

### Node.js ไม่ทำงาน
1. ตรวจสอบว่าติดตั้ง Node.js แล้ว
2. Restart terminal/command prompt
3. ตรวจสอบ PATH environment variable

### npm install ไม่ทำงาน
1. ลบโฟลเดอร์ `node_modules`
2. ลบไฟล์ `package-lock.json`
3. รัน `npm install` ใหม่

### Build ไม่สำเร็จ
1. ตรวจสอบ syntax error ในไฟล์ SCSS/JS
2. ตรวจสอบ console สำหรับ error messages
3. ลองรัน `npm run build` อีกครั้ง

## การสนับสนุน

หากมีปัญหาหรือข้อสงสัย:
1. ตรวจสอบ console สำหรับ error messages
2. ดูไฟล์ในโฟลเดอร์ `research/` สำหรับข้อมูลเพิ่มเติม
3. ตรวจสอบ documentation ของ tools ที่ใช้

## เครดิต

- แรงบันดาลใจจาก: EZMovie.me
- CSS Framework: Custom SCSS
- Build Tool: Webpack
- Icons: Feather Icons (https://feathericons.com/)

## License

MIT License - ดูไฟล์ LICENSE สำหรับรายละเอียด
