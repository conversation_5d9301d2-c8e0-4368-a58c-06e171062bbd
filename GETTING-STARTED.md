# 🚀 Getting Started - DoMovie Template

## ✨ ยินดีต้อนรับสู่ DoMovie Template!

Template นี้ได้รับการพัฒนาจากการวิจัยเว็บไซต์ EZMovie.me อย่างละเอียด และสร้างขึ้นเพื่อให้คุณสามารถสร้างเว็บไซต์ดูหนังออนไลน์ที่สวยงามและทันสมัยได้อย่างง่ายดาย

## 🎯 สิ่งที่คุณจะได้รับ

### ✅ พร้อมใช้งานทันที
- 🎨 **UI/UX สวยงาม**: Dark theme แบบ Netflix
- 📱 **Responsive Design**: รองรับทุกอุปกรณ์
- ⚡ **Performance**: โหลดเร็ว, smooth animations
- 🔍 **Search System**: ค้นหาแบบ real-time
- 🎬 **Hero Slider**: แสดงหนังเด่นแบบ auto-play
- 🃏 **Movie Cards**: การ์ดหนังที่สวยงามพร้อม hover effects

### 🛠️ เทคโนโลยีที่ใช้
- **HTML5**: Semantic markup
- **CSS3**: Modern CSS with variables
- **JavaScript ES6+**: Clean และ modular
- **Responsive**: Mobile-first design
- **Accessibility**: ARIA labels และ semantic elements

## 🚀 เริ่มต้นใช้งาน

### วิธีที่ 1: ใช้งานทันที (แนะนำ)
1. เปิดไฟล์ `index.html` ในเบราว์เซอร์
2. เว็บไซต์พร้อมใช้งาน!

### วิธีที่ 2: Development Mode
```bash
# ติดตั้ง Node.js ก่อน (https://nodejs.org/)
npm install
npm run dev
```

## 📁 โครงสร้างไฟล์

```
doomovie/
├── 📄 index.html              # หน้าเว็บหลัก
├── 📁 dist/                   # ไฟล์พร้อมใช้งาน
│   ├── 📁 css/
│   │   └── style.css          # CSS รวมทั้งหมด
│   ├── 📁 js/
│   │   └── main.js            # JavaScript รวมทั้งหมด
│   └── 📁 images/
│       └── placeholder.jpg    # รูปตัวอย่าง
├── 📁 src/                    # Source files
│   ├── 📁 scss/               # Sass files
│   ├── 📁 js/                 # JavaScript modules
│   └── 📄 *.html              # HTML templates
├── 📁 research/               # ข้อมูลการวิจัย
├── 📁 wordpress/              # WordPress theme (อนาคต)
├── 📄 README.md               # คู่มือหลัก
└── 📄 package.json            # NPM configuration
```

## 🎨 การปรับแต่ง

### เปลี่ยนสี
แก้ไขไฟล์ `dist/css/style.css` หรือ `src/scss/abstracts/_variables.scss`:

```css
:root {
  --color-primary: #e50914;        /* สีหลัก (แดง Netflix) */
  --color-bg-primary: #000000;     /* สีพื้นหลังหลัก */
  --color-text-primary: #ffffff;   /* สีข้อความหลัก */
}
```

### เปลี่ยนฟอนต์
```css
:root {
  --font-primary: 'Noto Sans Thai', Arial, sans-serif;
}
```

### เพิ่มหนัง
แก้ไขไฟล์ `dist/js/main.js` ในส่วน `mockMovies`:

```javascript
const mockMovies = [
  {
    id: 1,
    title: 'ชื่อหนัง',
    poster: '/path/to/poster.jpg',
    rating: 8.5,
    year: 2025
  }
  // เพิ่มหนังอื่นๆ
];
```

## 🔧 ฟีเจอร์ที่ใช้งานได้

### 1. 🎬 Hero Slider
- Auto-play ทุก 5 วินาที
- ปุ่ม Previous/Next
- Indicator dots
- Responsive

### 2. 🔍 Search System
- ค้นหาแบบ real-time
- Dropdown results
- Mobile-friendly
- Debounced input

### 3. 🃏 Movie Cards
- Hover effects สวยงาม
- Overlay actions
- Responsive grid
- Lazy loading ready

### 4. 📱 Mobile Navigation
- Bottom navigation bar
- Touch-friendly
- Icon + text labels

### 5. 🎯 Responsive Design
- Mobile-first approach
- Breakpoints: 576px, 768px, 992px, 1200px
- Flexible layouts

## 🎯 ขั้นตอนต่อไป

### Phase 1: เพิ่มเนื้อหา
1. **เพิ่มรูปภาพ**: ใส่โปสเตอร์หนังจริงใน `dist/images/`
2. **เพิ่มข้อมูลหนัง**: แก้ไข `mockMovies` ใน JavaScript
3. **ปรับแต่งสี**: เปลี่ยนสีให้เข้ากับแบรนด์

### Phase 2: ฟีเจอร์เพิ่มเติม
1. **Video Player**: เพิ่มตัวเล่นวิดีโอ
2. **Modal System**: Login, Movie details
3. **Advanced Search**: Filter, Categories
4. **User System**: Registration, Watchlist

### Phase 3: Backend Integration
1. **WordPress Theme**: แปลงเป็น WordPress
2. **Database**: เชื่อมต่อฐานข้อมูล
3. **API**: สร้าง REST API
4. **Admin Panel**: ระบบจัดการเนื้อหา

## 💡 Tips & Tricks

### การเพิ่มหนังใหม่
1. เตรียมรูปโปสเตอร์ขนาด 200x300px
2. เพิ่มข้อมูลใน `mockMovies` array
3. Refresh หน้าเว็บ

### การปรับแต่งสี
1. ใช้ CSS Variables สำหรับความสอดคล้อง
2. ทดสอบ contrast ratio สำหรับ accessibility
3. ใช้เครื่องมือ color picker

### การเพิ่ม Icons
1. ใช้ Feather Icons (https://feathericons.com/)
2. Copy SVG code
3. Paste ลงใน HTML

## 🐛 การแก้ไขปัญหา

### ปัญหาที่พบบ่อย

#### 1. รูปภาพไม่แสดง
```html
<!-- ตรวจสอบ path ให้ถูกต้อง -->
<img src="dist/images/movie-poster.jpg" alt="Movie Title">
```

#### 2. CSS ไม่ทำงาน
```html
<!-- ตรวจสอบ link tag -->
<link rel="stylesheet" href="dist/css/style.css">
```

#### 3. JavaScript ไม่ทำงาน
```html
<!-- ตรวจสอบ script tag -->
<script src="dist/js/main.js"></script>
```

#### 4. Mobile navigation ไม่แสดง
```css
/* ตรวจสอบ media query */
@media (max-width: 991px) {
  .mobile-nav { display: flex; }
}
```

## 📞 การสนับสนุน

### เอกสารเพิ่มเติม
- `README.md` - คู่มือหลัก
- `research/` - ข้อมูลการวิจัย
- `research/project-summary.md` - สรุปโปรเจค

### การพัฒนาต่อ
1. ศึกษาโครงสร้าง code ใน `src/`
2. ดู examples ใน `research/`
3. ทดลองแก้ไขและทดสอบ

## 🎉 ขอให้สนุกกับการพัฒนา!

Template นี้ถูกสร้างขึ้นด้วยความใส่ใจในทุกรายละเอียด เพื่อให้คุณสามารถสร้างเว็บไซต์ดูหนังที่สวยงามและใช้งานง่าย

หากมีคำถามหรือต้องการความช่วยเหลือ สามารถดูเอกสารเพิ่มเติมในโฟลเดอร์ `research/` หรือศึกษา code ใน `src/` เพื่อทำความเข้าใจการทำงานเพิ่มเติม

**Happy Coding! 🚀**
