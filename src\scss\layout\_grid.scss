/* Grid System */

.container {
  @include container;
  
  @include respond-to(sm) {
    max-width: var(--container-sm);
  }
  
  @include respond-to(md) {
    max-width: var(--container-md);
  }
  
  @include respond-to(lg) {
    max-width: var(--container-lg);
  }
  
  @include respond-to(xl) {
    max-width: var(--container-xl);
  }
  
  @include respond-to(2xl) {
    max-width: var(--container-2xl);
  }
}

.container-fluid {
  width: 100%;
  padding: 0 var(--spacing-md);
}

/* Movie Grid */
.movie-grid {
  @include grid(auto-fit, 180px, var(--spacing-md));
  
  @include respond-to(sm) {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
  
  @include respond-to(md) {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  }
  
  @include respond-to(lg) {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }
}

/* Section Grid */
.section-grid {
  display: grid;
  gap: var(--spacing-2xl);
  
  @include respond-to(md) {
    grid-template-columns: 2fr 1fr;
  }
}

/* Feature Grid */
.features__grid {
  @include grid(auto-fit, 300px, var(--spacing-xl));
}

/* Footer Grid */
.footer__content {
  display: grid;
  gap: var(--spacing-xl);
  
  @include respond-to(md) {
    grid-template-columns: 1fr 2fr;
  }
}

.footer__links {
  display: grid;
  gap: var(--spacing-lg);
  
  @include respond-to(sm) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @include respond-to(md) {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Utility Grid Classes */
.grid {
  display: grid;
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
.grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
.grid-cols-6 { grid-template-columns: repeat(6, 1fr); }

.gap-xs { gap: var(--spacing-xs); }
.gap-sm { gap: var(--spacing-sm); }
.gap-md { gap: var(--spacing-md); }
.gap-lg { gap: var(--spacing-lg); }
.gap-xl { gap: var(--spacing-xl); }
.gap-2xl { gap: var(--spacing-2xl); }
