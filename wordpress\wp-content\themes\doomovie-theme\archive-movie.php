<?php
/**
 * The template for displaying movie archive pages
 * 
 * @package DoMovie_Theme
 * @version 1.0.0
 */

get_header(); ?>

<main id="main" class="site-main movie-archive-main">
    
    <!-- Archive Header -->
    <section class="archive-header">
        <div class="container">
            <div class="archive-header-content">
                
                <h1 class="archive-title">
                    <?php
                    if (is_post_type_archive('movie')) {
                        _e('หนังทั้งหมด', 'doomovie-theme');
                    } else {
                        the_archive_title();
                    }
                    ?>
                </h1>

                <?php if (is_post_type_archive('movie')) : ?>
                    <p class="archive-description">
                        <?php _e('รวมหนังทุกประเภท ทุกแนว อัปเดตใหม่ทุกวัน', 'doomovie-theme'); ?>
                    </p>
                <?php else : ?>
                    <?php the_archive_description('<p class="archive-description">', '</p>'); ?>
                <?php endif; ?>

            </div>
        </div>
    </section>

    <!-- Filter and Search Section -->
    <section class="movie-filters">
        <div class="container">
            <div class="filters-wrapper">
                
                <!-- Search Form -->
                <div class="filter-search">
                    <form role="search" method="get" class="search-form" action="<?php echo home_url('/'); ?>">
                        <input type="search" 
                               class="search-field" 
                               placeholder="<?php _e('ค้นหาหนัง...', 'doomovie-theme'); ?>" 
                               value="<?php echo get_search_query(); ?>" 
                               name="s">
                        <input type="hidden" name="post_type" value="movie">
                        <button type="submit" class="search-submit">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                            </svg>
                        </button>
                    </form>
                </div>

                <!-- Filter Dropdowns -->
                <div class="filter-dropdowns">
                    
                    <!-- Genre Filter -->
                    <div class="filter-dropdown">
                        <select name="genre_filter" id="genre-filter" class="filter-select">
                            <option value=""><?php _e('ทุกประเภท', 'doomovie-theme'); ?></option>
                            <?php
                            $genres = get_terms(array(
                                'taxonomy' => 'movie_genre',
                                'hide_empty' => true,
                                'orderby' => 'name',
                                'order' => 'ASC'
                            ));
                            
                            if ($genres && !is_wp_error($genres)) :
                                foreach ($genres as $genre) :
                                    $selected = (is_tax('movie_genre', $genre->slug)) ? 'selected' : '';
                                    echo '<option value="' . $genre->slug . '" ' . $selected . '>' . $genre->name . '</option>';
                                endforeach;
                            endif;
                            ?>
                        </select>
                    </div>

                    <!-- Year Filter -->
                    <div class="filter-dropdown">
                        <select name="year_filter" id="year-filter" class="filter-select">
                            <option value=""><?php _e('ทุกปี', 'doomovie-theme'); ?></option>
                            <?php
                            $years = get_terms(array(
                                'taxonomy' => 'movie_year',
                                'hide_empty' => true,
                                'orderby' => 'name',
                                'order' => 'DESC'
                            ));
                            
                            if ($years && !is_wp_error($years)) :
                                foreach ($years as $year) :
                                    $selected = (is_tax('movie_year', $year->slug)) ? 'selected' : '';
                                    echo '<option value="' . $year->slug . '" ' . $selected . '>' . $year->name . '</option>';
                                endforeach;
                            endif;
                            ?>
                        </select>
                    </div>

                    <!-- Country Filter -->
                    <div class="filter-dropdown">
                        <select name="country_filter" id="country-filter" class="filter-select">
                            <option value=""><?php _e('ทุกประเทศ', 'doomovie-theme'); ?></option>
                            <?php
                            $countries = get_terms(array(
                                'taxonomy' => 'movie_country',
                                'hide_empty' => true,
                                'orderby' => 'name',
                                'order' => 'ASC'
                            ));
                            
                            if ($countries && !is_wp_error($countries)) :
                                foreach ($countries as $country) :
                                    $selected = (is_tax('movie_country', $country->slug)) ? 'selected' : '';
                                    echo '<option value="' . $country->slug . '" ' . $selected . '>' . $country->name . '</option>';
                                endforeach;
                            endif;
                            ?>
                        </select>
                    </div>

                    <!-- Sort Filter -->
                    <div class="filter-dropdown">
                        <select name="sort_filter" id="sort-filter" class="filter-select">
                            <option value="date"><?php _e('ใหม่ล่าสุด', 'doomovie-theme'); ?></option>
                            <option value="title"><?php _e('ชื่อ A-Z', 'doomovie-theme'); ?></option>
                            <option value="views"><?php _e('ยอดนิยม', 'doomovie-theme'); ?></option>
                            <option value="rating"><?php _e('คะแนน IMDB', 'doomovie-theme'); ?></option>
                            <option value="year"><?php _e('ปีที่ออกฉาย', 'doomovie-theme'); ?></option>
                        </select>
                    </div>

                </div>

                <!-- View Toggle -->
                <div class="view-toggle">
                    <button class="view-btn grid-view active" data-view="grid" aria-label="<?php _e('มุมมองตาราง', 'doomovie-theme'); ?>">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M4 11h5V5H4v6zm0 7h5v-6H4v6zm6 0h5v-6h-5v6zm6 0h5v-6h-5v6zm-6-7h5V5h-5v6zm6-6v6h5V5h-5z"/>
                        </svg>
                    </button>
                    <button class="view-btn list-view" data-view="list" aria-label="<?php _e('มุมมองรายการ', 'doomovie-theme'); ?>">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M4 14h4v-4H4v4zm0 5h4v-4H4v4zM4 9h4V5H4v4zm5 5h12v-4H9v4zm0 5h12v-4H9v4zM9 5v4h12V5H9z"/>
                        </svg>
                    </button>
                </div>

            </div>
        </div>
    </section>

    <!-- Movies Grid/List Section -->
    <section class="movies-content">
        <div class="container">
            
            <?php if (have_posts()) : ?>
                
                <!-- Results Info -->
                <div class="results-info">
                    <p class="results-count">
                        <?php
                        global $wp_query;
                        $total = $wp_query->found_posts;
                        printf(
                            _n('พบ %s หนัง', 'พบ %s หนัง', $total, 'doomovie-theme'),
                            '<strong>' . number_format($total) . '</strong>'
                        );
                        ?>
                    </p>
                </div>

                <!-- Movies Grid -->
                <div class="movies-grid" id="movies-container">
                    <?php
                    while (have_posts()) :
                        the_post();
                        get_template_part('template-parts/content/movie-card');
                    endwhile;
                    ?>
                </div>

                <!-- Load More Button -->
                <div class="load-more-section">
                    <?php
                    $next_page = get_next_posts_page_link();
                    if ($next_page) :
                    ?>
                        <button class="btn btn-primary load-more-btn" 
                                data-next-page="<?php echo $next_page; ?>"
                                data-max-pages="<?php echo $wp_query->max_num_pages; ?>"
                                data-current-page="<?php echo get_query_var('paged') ?: 1; ?>">
                            <span class="load-more-text"><?php _e('โหลดเพิ่มเติม', 'doomovie-theme'); ?></span>
                            <span class="load-more-loading" style="display: none;">
                                <img src="<?php echo DOOMOVIE_THEME_URL; ?>/assets/images/loading.gif" alt="Loading...">
                                <?php _e('กำลังโหลด...', 'doomovie-theme'); ?>
                            </span>
                        </button>
                    <?php endif; ?>
                </div>

                <!-- Pagination -->
                <nav class="pagination-nav" aria-label="<?php _e('Movie pagination', 'doomovie-theme'); ?>">
                    <?php
                    echo paginate_links(array(
                        'prev_text' => '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/></svg>' . __('ก่อนหน้า', 'doomovie-theme'),
                        'next_text' => __('ถัดไป', 'doomovie-theme') . '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/></svg>',
                        'type' => 'list',
                        'end_size' => 2,
                        'mid_size' => 1,
                    ));
                    ?>
                </nav>

            <?php else : ?>
                
                <!-- No Results -->
                <div class="no-results">
                    <div class="no-results-content">
                        <div class="no-results-icon">
                            <svg width="64" height="64" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                            </svg>
                        </div>
                        <h2><?php _e('ไม่พบหนังที่ค้นหา', 'doomovie-theme'); ?></h2>
                        <p><?php _e('ขออภัย ไม่พบหนังที่ตรงกับเงื่อนไขที่คุณค้นหา กรุณาลองเปลี่ยนคำค้นหาหรือตัวกรอง', 'doomovie-theme'); ?></p>
                        
                        <!-- Suggestions -->
                        <div class="search-suggestions">
                            <h3><?php _e('ลองค้นหาหนังเหล่านี้', 'doomovie-theme'); ?></h3>
                            
                            <?php
                            // Get popular movies as suggestions
                            $suggestions = get_popular_movies(6);
                            if ($suggestions->have_posts()) :
                            ?>
                                <div class="suggestions-grid">
                                    <?php
                                    while ($suggestions->have_posts()) :
                                        $suggestions->the_post();
                                        get_template_part('template-parts/content/movie-card');
                                    endwhile;
                                    wp_reset_postdata();
                                    ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Reset Filters -->
                        <div class="reset-filters">
                            <a href="<?php echo get_post_type_archive_link('movie'); ?>" class="btn btn-primary">
                                <?php _e('ดูหนังทั้งหมด', 'doomovie-theme'); ?>
                            </a>
                        </div>
                    </div>
                </div>

            <?php endif; ?>

        </div>
    </section>

</main>

<!-- Filter Data for JavaScript -->
<script type="application/json" id="filter-data">
{
    "ajaxUrl": "<?php echo admin_url('admin-ajax.php'); ?>",
    "nonce": "<?php echo wp_create_nonce('movie_filter_nonce'); ?>",
    "currentFilters": {
        "genre": "<?php echo is_tax('movie_genre') ? get_queried_object()->slug : ''; ?>",
        "year": "<?php echo is_tax('movie_year') ? get_queried_object()->slug : ''; ?>",
        "country": "<?php echo is_tax('movie_country') ? get_queried_object()->slug : ''; ?>",
        "search": "<?php echo get_search_query(); ?>"
    }
}
</script>

<?php get_footer(); ?>
