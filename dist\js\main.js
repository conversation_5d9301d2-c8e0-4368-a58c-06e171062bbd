// DoMovie Template - Main JavaScript

// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all components
    initMobileMenu();
    initSearch();
    initHeroSlider();
    initMovieCards();
    initScrollEffects();
    
    console.log('DoMovie Template initialized');
});

// Mobile Menu Toggle
function initMobileMenu() {
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const navbar = document.querySelector('.navbar');
    
    if (mobileMenuToggle) {
        mobileMenuToggle.addEventListener('click', function() {
            this.classList.toggle('active');
            navbar.classList.toggle('mobile-menu-open');
        });
    }
}

// Search Functionality
function initSearch() {
    const searchToggles = document.querySelectorAll('.search-toggle');
    const searchBar = document.getElementById('searchBar');
    const searchInput = document.getElementById('searchInput');
    const searchClose = document.getElementById('searchClose');
    const searchResults = document.getElementById('searchResults');
    
    // Toggle search bar
    searchToggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            if (searchBar) {
                searchBar.classList.toggle('active');
                if (searchBar.classList.contains('active')) {
                    searchInput.focus();
                }
            }
        });
    });
    
    // Close search bar
    if (searchClose) {
        searchClose.addEventListener('click', function() {
            searchBar.classList.remove('active');
            searchInput.value = '';
            searchResults.innerHTML = '';
        });
    }
    
    // Search input handler
    if (searchInput) {
        let searchTimeout;
        
        searchInput.addEventListener('input', function() {
            const query = this.value.trim();
            
            clearTimeout(searchTimeout);
            
            if (query.length >= 2) {
                searchTimeout = setTimeout(() => {
                    performSearch(query);
                }, 300);
            } else {
                searchResults.innerHTML = '';
            }
        });
        
        // Handle Enter key
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                const query = this.value.trim();
                if (query) {
                    window.location.href = `/search?q=${encodeURIComponent(query)}`;
                }
            }
        });
    }
}

// Perform search
function performSearch(query) {
    const searchResults = document.getElementById('searchResults');
    
    // Show loading
    searchResults.innerHTML = '<div class="search-loading">กำลังค้นหา...</div>';
    
    // Mock search results (replace with actual API call)
    setTimeout(() => {
        const mockResults = [
            { id: 1, title: 'Jurassic World Rebirth (2025)', url: '/movie/jurassic-world-rebirth-2025', poster: '/images/movies/jurassic-world.jpg' },
            { id: 2, title: 'F1 The Movie (2025)', url: '/movie/f1-the-movie-2025', poster: '/images/movies/f1-movie.jpg' },
            { id: 3, title: 'Lilo & Stitch (2025)', url: '/movie/lilo-stitch-2025', poster: '/images/movies/lilo-stitch.jpg' }
        ];
        
        if (mockResults.length > 0) {
            searchResults.innerHTML = mockResults.map(movie => `
                <div class="search-result-item">
                    <img src="${movie.poster}" alt="${movie.title}" class="search-result-poster" onerror="this.src='/images/placeholder.jpg'">
                    <div class="search-result-info">
                        <h4 class="search-result-title">${movie.title}</h4>
                        <a href="${movie.url}" class="search-result-link">ดูหนัง</a>
                    </div>
                </div>
            `).join('');
        } else {
            searchResults.innerHTML = '<div class="search-no-results">ไม่พบผลการค้นหา</div>';
        }
    }, 500);
}

// Hero Slider
function initHeroSlider() {
    const heroSlider = document.getElementById('heroSlider');
    const heroIndicators = document.getElementById('heroIndicators');
    const prevBtn = document.querySelector('.hero__prev');
    const nextBtn = document.querySelector('.hero__next');
    
    if (!heroSlider) return;
    
    // Mock hero data
    const heroMovies = [
        {
            id: 1,
            title: 'Jurassic World Rebirth (2025)',
            description: 'ยุคใหม่ของไดโนเสาร์ได้เริ่มต้นแล้ว การกลับมาของสายพันธุ์ล้านปี เมื่อเหล่าไดโนเสาร์จะช่วยชีวิตมนุษยชาติได้',
            backdrop: '/images/hero/jurassic-world-backdrop.jpg',
            url: '/movie/jurassic-world-rebirth-2025'
        },
        {
            id: 2,
            title: 'Squid Game 3 (2025)',
            description: 'กีฮุนกลับเข้าสู่เกมอีกครั้ง เพื่อหยุดยั้งองค์กรลับที่อยู่เบื้องหลัง พร้อมเผชิญหน้ากับศัตรูเก่าอย่าง ฟรอนต์แมน',
            backdrop: '/images/hero/squid-game-backdrop.jpg',
            url: '/movie/squid-game-3-2025'
        },
        {
            id: 3,
            title: 'Ironheart (2025)',
            description: 'หลังกลับจากวากานดา ริริ วิลเลียมส์ ตั้งใจสร้างอนาคตใหม่ที่ชิคาโก แต่กลับต้องเผชิญกับศัตรูที่รวมพลังเวทมนตร์และเทคโนโลยี',
            backdrop: '/images/hero/ironheart-backdrop.jpg',
            url: '/movie/ironheart-2025'
        }
    ];
    
    let currentSlide = 0;
    
    // Create hero slides
    heroSlider.innerHTML = heroMovies.map((movie, index) => `
        <div class="hero__slide ${index === 0 ? 'active' : ''}" style="background-image: url('${movie.backdrop}')">
            <div class="hero__content">
                <div class="container">
                    <h1 class="hero__title">${movie.title}</h1>
                    <p class="hero__description">${movie.description}</p>
                    <div class="hero__actions">
                        <a href="${movie.url}" class="btn btn--primary">ดูหนัง</a>
                        <button class="btn btn--secondary" onclick="addToWatchlist(${movie.id})">รายการของฉัน</button>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
    
    // Create indicators
    if (heroIndicators) {
        heroIndicators.innerHTML = heroMovies.map((_, index) => `
            <button class="hero__indicator ${index === 0 ? 'active' : ''}" data-slide="${index}"></button>
        `).join('');
        
        // Indicator click handlers
        heroIndicators.addEventListener('click', function(e) {
            if (e.target.classList.contains('hero__indicator')) {
                const slideIndex = parseInt(e.target.dataset.slide);
                goToSlide(slideIndex);
            }
        });
    }
    
    // Navigation buttons
    if (prevBtn) {
        prevBtn.addEventListener('click', () => {
            currentSlide = currentSlide === 0 ? heroMovies.length - 1 : currentSlide - 1;
            goToSlide(currentSlide);
        });
    }
    
    if (nextBtn) {
        nextBtn.addEventListener('click', () => {
            currentSlide = currentSlide === heroMovies.length - 1 ? 0 : currentSlide + 1;
            goToSlide(currentSlide);
        });
    }
    
    // Auto-play
    setInterval(() => {
        currentSlide = currentSlide === heroMovies.length - 1 ? 0 : currentSlide + 1;
        goToSlide(currentSlide);
    }, 5000);
    
    function goToSlide(index) {
        const slides = document.querySelectorAll('.hero__slide');
        const indicators = document.querySelectorAll('.hero__indicator');
        
        slides.forEach((slide, i) => {
            slide.classList.toggle('active', i === index);
        });
        
        indicators.forEach((indicator, i) => {
            indicator.classList.toggle('active', i === index);
        });
        
        currentSlide = index;
    }
}

// Movie Cards
function initMovieCards() {
    // Mock movie data
    const mockMovies = [
        { id: 1, title: 'Jurassic World Rebirth (2025)', poster: '/images/movies/jurassic-world.jpg', rating: 6.5, year: 2025 },
        { id: 2, title: 'F1 The Movie (2025)', poster: '/images/movies/f1-movie.jpg', rating: 7.2, year: 2025 },
        { id: 3, title: 'Lilo & Stitch (2025)', poster: '/images/movies/lilo-stitch.jpg', rating: 8.1, year: 2025 },
        { id: 4, title: 'M3GAN 2.0 (2025)', poster: '/images/movies/m3gan.jpg', rating: 6.8, year: 2025 },
        { id: 5, title: 'How to Train Your Dragon (2025)', poster: '/images/movies/httyd.jpg', rating: 8.5, year: 2025 },
        { id: 6, title: '28 Years Later (2025)', poster: '/images/movies/28-years-later.jpg', rating: 7.3, year: 2025 }
    ];
    
    // Populate movie grids
    const movieGrids = document.querySelectorAll('.movie-grid');
    
    movieGrids.forEach(grid => {
        grid.innerHTML = mockMovies.map(movie => createMovieCard(movie)).join('');
    });
}

// Create movie card HTML
function createMovieCard(movie) {
    return `
        <div class="movie-card" data-movie-id="${movie.id}">
            <div class="movie-card__poster">
                <img src="${movie.poster}" alt="${movie.title}" onerror="this.src='/images/placeholder.jpg'">
                <div class="movie-card__overlay">
                    <div class="movie-card__actions">
                        <button class="btn btn--primary btn--sm" onclick="window.location.href='/movie/${movie.id}'">ดูหนัง</button>
                        <button class="btn btn--secondary btn--sm" onclick="addToWatchlist(${movie.id})">+</button>
                    </div>
                </div>
            </div>
            <div class="movie-card__info">
                <h3 class="movie-card__title">${movie.title}</h3>
                <div class="movie-card__meta">
                    <span class="movie-card__year">${movie.year}</span>
                    <span class="movie-card__rating">⭐ ${movie.rating}</span>
                </div>
            </div>
        </div>
    `;
}

// Scroll Effects
function initScrollEffects() {
    const header = document.querySelector('.header');
    
    window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    });
}

// Utility Functions
function addToWatchlist(movieId) {
    // Mock function - replace with actual implementation
    console.log('Added movie', movieId, 'to watchlist');
    
    // Show notification (you can implement a toast notification here)
    alert('เพิ่มในรายการของฉันแล้ว');
}

function showModal(modalType, content) {
    // Mock function - implement modal system
    console.log('Show modal:', modalType, content);
}

// Loading utility
function showLoading() {
    const loading = document.getElementById('loading');
    if (loading) {
        loading.style.display = 'flex';
    }
}

function hideLoading() {
    const loading = document.getElementById('loading');
    if (loading) {
        loading.style.display = 'none';
    }
}

// Export functions for global use
window.addToWatchlist = addToWatchlist;
window.showModal = showModal;
window.showLoading = showLoading;
window.hideLoading = hideLoading;
