const puppeteer = require('puppeteer');
const cheerio = require('cheerio');
const fs = require('fs-extra');
const path = require('path');
const axios = require('axios');

class EZMovieAnalyzer {
    constructor() {
        this.baseUrl = 'https://ezmovie.me';
        this.outputDir = './research/detailed-analysis';
        this.browser = null;
        this.page = null;
    }

    async init() {
        console.log('🚀 เริ่มต้นการวิเคราะห์ EZMovie.me...');
        
        // สร้างโฟลเดอร์สำหรับเก็บผลลัพธ์
        await fs.ensureDir(this.outputDir);
        
        // เปิด browser
        this.browser = await puppeteer.launch({
            headless: 'new',
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        this.page = await this.browser.newPage();
        
        // ตั้งค่า User Agent
        await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
    }

    async analyzeHomepage() {
        console.log('📄 วิเคราะห์หน้าแรก...');
        
        try {
            await this.page.goto(this.baseUrl, { waitUntil: 'networkidle2', timeout: 30000 });
            
            // รอให้เนื้อหาโหลดเสร็จ
            await this.page.waitForTimeout(3000);
            
            const analysis = await this.page.evaluate(() => {
                const data = {
                    title: document.title,
                    meta: {},
                    structure: {},
                    assets: {
                        css: [],
                        js: [],
                        images: [],
                        fonts: []
                    },
                    content: {
                        navigation: [],
                        heroSection: {},
                        movieSections: [],
                        footer: {}
                    }
                };

                // Meta tags
                document.querySelectorAll('meta').forEach(meta => {
                    const name = meta.getAttribute('name') || meta.getAttribute('property');
                    const content = meta.getAttribute('content');
                    if (name && content) {
                        data.meta[name] = content;
                    }
                });

                // CSS files
                document.querySelectorAll('link[rel="stylesheet"]').forEach(link => {
                    data.assets.css.push({
                        href: link.href,
                        media: link.media || 'all'
                    });
                });

                // JavaScript files
                document.querySelectorAll('script[src]').forEach(script => {
                    data.assets.js.push({
                        src: script.src,
                        async: script.async,
                        defer: script.defer
                    });
                });

                // Images
                document.querySelectorAll('img').forEach(img => {
                    data.assets.images.push({
                        src: img.src,
                        alt: img.alt,
                        loading: img.loading,
                        className: img.className
                    });
                });

                // Navigation
                const nav = document.querySelector('nav') || document.querySelector('.navigation') || document.querySelector('.menu');
                if (nav) {
                    nav.querySelectorAll('a').forEach(link => {
                        data.content.navigation.push({
                            text: link.textContent.trim(),
                            href: link.href,
                            className: link.className
                        });
                    });
                }

                // Hero section
                const hero = document.querySelector('.hero') || document.querySelector('.slider') || document.querySelector('.banner');
                if (hero) {
                    data.content.heroSection = {
                        html: hero.innerHTML.substring(0, 1000), // จำกัดขนาด
                        className: hero.className
                    };
                }

                // Movie sections
                document.querySelectorAll('.movie-section, .section, .content-section').forEach(section => {
                    const title = section.querySelector('h2, h3, .section-title');
                    data.content.movieSections.push({
                        title: title ? title.textContent.trim() : '',
                        className: section.className,
                        movieCount: section.querySelectorAll('.movie-card, .movie-item, .item').length
                    });
                });

                return data;
            });

            // บันทึกผลลัพธ์
            await fs.writeJSON(path.join(this.outputDir, 'homepage-analysis.json'), analysis, { spaces: 2 });
            console.log('✅ วิเคราะห์หน้าแรกเสร็จสิ้น');
            
            return analysis;
        } catch (error) {
            console.error('❌ เกิดข้อผิดพลาดในการวิเคราะห์หน้าแรก:', error.message);
            return null;
        }
    }

    async analyzeMovieDetailPage() {
        console.log('🎬 วิเคราะห์หน้ารายละเอียดหนัง...');
        
        try {
            // หาลิงก์หนังจากหน้าแรก
            const movieLinks = await this.page.evaluate(() => {
                const links = [];
                document.querySelectorAll('a[href*="/movie/"]').forEach(link => {
                    if (links.length < 3) { // เอาแค่ 3 ลิงก์แรก
                        links.push(link.href);
                    }
                });
                return links;
            });

            if (movieLinks.length === 0) {
                console.log('⚠️ ไม่พบลิงก์หนัง');
                return null;
            }

            const movieAnalysis = [];
            
            for (const movieUrl of movieLinks.slice(0, 1)) { // วิเคราะห์แค่หนังแรก
                console.log(`📽️ วิเคราะห์หนัง: ${movieUrl}`);
                
                await this.page.goto(movieUrl, { waitUntil: 'networkidle2', timeout: 30000 });
                await this.page.waitForTimeout(2000);

                const analysis = await this.page.evaluate(() => {
                    return {
                        url: window.location.href,
                        title: document.title,
                        movieInfo: {
                            title: document.querySelector('h1, .movie-title')?.textContent?.trim(),
                            poster: document.querySelector('.movie-poster img, .poster img')?.src,
                            rating: document.querySelector('.rating, .imdb-rating')?.textContent?.trim(),
                            year: document.querySelector('.year, .release-year')?.textContent?.trim(),
                            duration: document.querySelector('.duration, .runtime')?.textContent?.trim(),
                            genre: Array.from(document.querySelectorAll('.genre, .category')).map(el => el.textContent.trim()),
                            synopsis: document.querySelector('.synopsis, .description, .plot')?.textContent?.trim()
                        },
                        videoPlayer: {
                            present: !!document.querySelector('video, iframe, .video-player'),
                            type: document.querySelector('video') ? 'video' : 
                                  document.querySelector('iframe') ? 'iframe' : 'unknown'
                        },
                        actionButtons: Array.from(document.querySelectorAll('button, .btn')).map(btn => ({
                            text: btn.textContent.trim(),
                            className: btn.className
                        }))
                    };
                });

                movieAnalysis.push(analysis);
            }

            await fs.writeJSON(path.join(this.outputDir, 'movie-detail-analysis.json'), movieAnalysis, { spaces: 2 });
            console.log('✅ วิเคราะห์หน้ารายละเอียดหนังเสร็จสิ้น');
            
            return movieAnalysis;
        } catch (error) {
            console.error('❌ เกิดข้อผิดพลาดในการวิเคราะห์หน้ารายละเอียดหนัง:', error.message);
            return null;
        }
    }

    async analyzeCSSStructure() {
        console.log('🎨 วิเคราะห์โครงสร้าง CSS...');
        
        try {
            await this.page.goto(this.baseUrl, { waitUntil: 'networkidle2' });
            
            const cssAnalysis = await this.page.evaluate(() => {
                const styles = {
                    colors: new Set(),
                    fonts: new Set(),
                    classes: new Set(),
                    ids: new Set()
                };

                // วิเคราะห์ computed styles ของ elements สำคัญ
                const importantElements = document.querySelectorAll('body, header, nav, main, footer, .movie-card, .btn');
                
                importantElements.forEach(el => {
                    const computed = window.getComputedStyle(el);
                    
                    // เก็บสี
                    styles.colors.add(computed.color);
                    styles.colors.add(computed.backgroundColor);
                    styles.colors.add(computed.borderColor);
                    
                    // เก็บ font
                    styles.fonts.add(computed.fontFamily);
                    
                    // เก็บ class names
                    if (el.className) {
                        el.className.split(' ').forEach(cls => {
                            if (cls.trim()) styles.classes.add(cls.trim());
                        });
                    }
                    
                    // เก็บ IDs
                    if (el.id) styles.ids.add(el.id);
                });

                return {
                    colors: Array.from(styles.colors).filter(color => color && color !== 'rgba(0, 0, 0, 0)'),
                    fonts: Array.from(styles.fonts),
                    classes: Array.from(styles.classes).slice(0, 50), // จำกัดจำนวน
                    ids: Array.from(styles.ids)
                };
            });

            await fs.writeJSON(path.join(this.outputDir, 'css-analysis.json'), cssAnalysis, { spaces: 2 });
            console.log('✅ วิเคราะห์โครงสร้าง CSS เสร็จสิ้น');
            
            return cssAnalysis;
        } catch (error) {
            console.error('❌ เกิดข้อผิดพลาดในการวิเคราะห์ CSS:', error.message);
            return null;
        }
    }

    async generateReport() {
        console.log('📊 สร้างรายงานสรุป...');
        
        const reportData = {
            timestamp: new Date().toISOString(),
            website: this.baseUrl,
            analysis: {
                homepage: null,
                movieDetail: null,
                css: null
            },
            recommendations: []
        };

        try {
            // อ่านไฟล์ที่วิเคราะห์แล้ว
            const files = ['homepage-analysis.json', 'movie-detail-analysis.json', 'css-analysis.json'];
            
            for (const file of files) {
                const filePath = path.join(this.outputDir, file);
                if (await fs.pathExists(filePath)) {
                    const data = await fs.readJSON(filePath);
                    const key = file.replace('-analysis.json', '').replace('-', '');
                    reportData.analysis[key] = data;
                }
            }

            // สร้างคำแนะนำ
            reportData.recommendations = [
                'ใช้ Dark Theme เป็นหลักตามที่วิเคราะห์ได้',
                'สร้าง Movie Card Component ที่มี Poster, Title, Rating, Year',
                'ใช้ Grid Layout สำหรับแสดงรายการหนัง',
                'เพิ่ม Lazy Loading สำหรับรูปภาพ',
                'สร้าง Modal สำหรับ Video Player',
                'ใช้ Responsive Design สำหรับ Mobile',
                'เพิ่ม Search และ Filter functionality'
            ];

            await fs.writeJSON(path.join(this.outputDir, 'complete-analysis-report.json'), reportData, { spaces: 2 });
            
            // สร้างรายงาน Markdown
            await this.generateMarkdownReport(reportData);
            
            console.log('✅ สร้างรายงานสรุปเสร็จสิ้น');
            
        } catch (error) {
            console.error('❌ เกิดข้อผิดพลาดในการสร้างรายงาน:', error.message);
        }
    }

    async generateMarkdownReport(data) {
        const markdown = `# รายงานการวิเคราะห์ EZMovie.me แบบละเอียด

## ข้อมูลการวิเคราะห์
- **เว็บไซต์**: ${data.website}
- **วันที่วิเคราะห์**: ${new Date(data.timestamp).toLocaleString('th-TH')}

## สรุปผลการวิเคราะห์

### 1. โครงสร้างหน้าแรก
${data.analysis.homepage ? `
- **Title**: ${data.analysis.homepage.title}
- **จำนวน CSS Files**: ${data.analysis.homepage.assets.css.length}
- **จำนวน JS Files**: ${data.analysis.homepage.assets.js.length}
- **จำนวนรูปภาพ**: ${data.analysis.homepage.assets.images.length}
- **จำนวน Navigation Items**: ${data.analysis.homepage.content.navigation.length}
- **จำนวน Movie Sections**: ${data.analysis.homepage.content.movieSections.length}
` : 'ไม่สามารถวิเคราะห์ได้'}

### 2. หน้ารายละเอียดหนัง
${data.analysis.movieDetail ? `
- **จำนวนหนังที่วิเคราะห์**: ${data.analysis.movieDetail.length}
- **มี Video Player**: ${data.analysis.movieDetail[0]?.videoPlayer.present ? 'ใช่' : 'ไม่'}
- **ประเภท Player**: ${data.analysis.movieDetail[0]?.videoPlayer.type || 'ไม่ระบุ'}
` : 'ไม่สามารถวิเคราะห์ได้'}

### 3. โครงสร้าง CSS
${data.analysis.css ? `
- **จำนวนสีที่ใช้**: ${data.analysis.css.colors.length}
- **จำนวน Font Family**: ${data.analysis.css.fonts.length}
- **จำนวน CSS Classes**: ${data.analysis.css.classes.length}
` : 'ไม่สามารถวิเคราะห์ได้'}

## คำแนะนำสำหรับการพัฒนา

${data.recommendations.map(rec => `- ${rec}`).join('\n')}

## ไฟล์ข้อมูลรายละเอียด

- \`homepage-analysis.json\` - ข้อมูลหน้าแรกแบบละเอียด
- \`movie-detail-analysis.json\` - ข้อมูลหน้ารายละเอียดหนัง
- \`css-analysis.json\` - ข้อมูลโครงสร้าง CSS
- \`complete-analysis-report.json\` - รายงานสรุปทั้งหมด

---
*รายงานนี้สร้างโดยระบบวิเคราะห์อัตโนมัติ*
`;

        await fs.writeFile(path.join(this.outputDir, 'detailed-analysis-report.md'), markdown);
    }

    async run() {
        try {
            await this.init();
            
            // วิเคราะห์ทีละส่วน
            await this.analyzeHomepage();
            await this.analyzeMovieDetailPage();
            await this.analyzeCSSStructure();
            
            // สร้างรายงานสรุป
            await this.generateReport();
            
            console.log('🎉 การวิเคราะห์เสร็จสิ้นแล้ว!');
            console.log(`📁 ผลลัพธ์อยู่ในโฟลเดอร์: ${this.outputDir}`);
            
        } catch (error) {
            console.error('❌ เกิดข้อผิดพลาด:', error.message);
        } finally {
            if (this.browser) {
                await this.browser.close();
            }
        }
    }
}

// รันการวิเคราะห์
if (require.main === module) {
    const analyzer = new EZMovieAnalyzer();
    analyzer.run();
}

module.exports = EZMovieAnalyzer;
