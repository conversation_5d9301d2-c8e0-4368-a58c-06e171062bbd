<?php
/**
 * Template part for displaying movie cards
 * 
 * @package DoMovie_Theme
 * @version 1.0.0
 */

$movie_id = get_the_ID();
$poster_url = get_movie_poster($movie_id);
$year = get_movie_year($movie_id);
$rating = get_movie_rating($movie_id);
$duration = get_movie_duration($movie_id);
$genres = get_the_terms($movie_id, 'movie_genre');
$imdb_rating = get_movie_imdb_rating($movie_id);
$is_featured = is_featured_movie($movie_id);
?>

<article id="movie-<?php echo $movie_id; ?>" class="movie-card <?php echo $is_featured ? 'featured' : ''; ?>" data-movie-id="<?php echo $movie_id; ?>">
    
    <!-- Movie Poster -->
    <div class="movie-poster">
        <div class="poster-container">
            <!-- Lazy Loading Placeholder -->
            <img src="<?php echo DOOMOVIE_THEME_URL; ?>/assets/images/placeholders/movie-loading.gif" 
                 data-src="<?php echo $poster_url; ?>" 
                 alt="<?php the_title_attribute(); ?>" 
                 class="poster-image lazy-load"
                 loading="lazy">
            
            <!-- Movie Quality Badge -->
            <?php if ($quality = get_post_meta($movie_id, 'quality', true)) : ?>
                <div class="quality-badge">
                    <img src="<?php echo DOOMOVIE_THEME_URL; ?>/assets/images/icons/hd-badge.png" alt="HD" class="quality-icon">
                </div>
            <?php endif; ?>

            <!-- Featured Badge -->
            <?php if ($is_featured) : ?>
                <div class="featured-badge">
                    <span><?php _e('แนะนำ', 'doomovie-theme'); ?></span>
                </div>
            <?php endif; ?>

            <!-- Movie Overlay -->
            <div class="movie-overlay">
                <div class="overlay-content">
                    
                    <!-- Play Button -->
                    <a href="<?php the_permalink(); ?>" class="play-button" aria-label="<?php _e('ดูหนัง', 'doomovie-theme'); ?> <?php the_title(); ?>">
                        <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M8 5v14l11-7z"/>
                        </svg>
                        <span class="play-text"><?php _e('ดูหนัง', 'doomovie-theme'); ?></span>
                    </a>

                    <!-- Action Buttons -->
                    <div class="action-buttons">
                        
                        <!-- Add to Watchlist -->
                        <?php if (is_user_logged_in()) : ?>
                            <button class="btn-action add-to-watchlist" 
                                    data-movie-id="<?php echo $movie_id; ?>" 
                                    aria-label="<?php _e('เพิ่มในรายการของฉัน', 'doomovie-theme'); ?>">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                                </svg>
                                <span><?php _e('รายการของฉัน', 'doomovie-theme'); ?></span>
                            </button>
                        <?php else : ?>
                            <button class="btn-action login-required" 
                                    aria-label="<?php _e('เข้าสู่ระบบเพื่อเพิ่มในรายการ', 'doomovie-theme'); ?>">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                                </svg>
                                <span><?php _e('รายการของฉัน', 'doomovie-theme'); ?></span>
                            </button>
                        <?php endif; ?>

                        <!-- Share Button -->
                        <button class="btn-action share-movie" 
                                data-movie-id="<?php echo $movie_id; ?>" 
                                data-movie-title="<?php the_title_attribute(); ?>"
                                data-movie-url="<?php the_permalink(); ?>"
                                aria-label="<?php _e('แชร์หนัง', 'doomovie-theme'); ?>">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92s2.92-1.31 2.92-2.92-1.31-2.92-2.92-2.92z"/>
                            </svg>
                            <span><?php _e('แชร์', 'doomovie-theme'); ?></span>
                        </button>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Movie Information -->
    <div class="movie-info">
        
        <!-- Movie Title -->
        <h3 class="movie-title">
            <a href="<?php the_permalink(); ?>" title="<?php the_title_attribute(); ?>">
                <?php the_title(); ?>
            </a>
        </h3>

        <!-- Movie Meta -->
        <div class="movie-meta">
            
            <!-- Year -->
            <?php if ($year) : ?>
                <span class="meta-item year">
                    <span class="meta-label"><?php _e('ปี', 'doomovie-theme'); ?></span>
                    <span class="meta-value"><?php echo $year; ?></span>
                </span>
            <?php endif; ?>

            <!-- Age Rating -->
            <?php if ($rating) : ?>
                <span class="meta-item age-rating">
                    <img src="<?php echo DOOMOVIE_THEME_URL; ?>/assets/images/icons/rate-<?php echo $rating; ?>.png" 
                         alt="<?php echo $rating; ?>" 
                         class="rating-icon">
                </span>
            <?php endif; ?>

            <!-- Duration -->
            <?php if ($duration) : ?>
                <span class="meta-item duration">
                    <span class="meta-value"><?php echo $duration; ?></span>
                </span>
            <?php endif; ?>

            <!-- IMDB Rating -->
            <?php if ($imdb_rating) : ?>
                <span class="meta-item imdb-rating">
                    <span class="meta-label">IMDB:</span>
                    <span class="meta-value"><?php echo $imdb_rating; ?></span>
                </span>
            <?php endif; ?>

        </div>

        <!-- Movie Genres -->
        <?php if ($genres && !is_wp_error($genres)) : ?>
            <div class="movie-genres">
                <?php foreach ($genres as $genre) : ?>
                    <a href="<?php echo get_term_link($genre); ?>" class="genre-tag">
                        <?php echo $genre->name; ?>
                    </a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <!-- Movie Excerpt -->
        <div class="movie-excerpt">
            <?php
            $excerpt = get_the_excerpt();
            if ($excerpt) {
                echo '<p>' . wp_trim_words($excerpt, 20, '...') . '</p>';
            }
            ?>
        </div>

        <!-- Movie Tags -->
        <?php
        $tags = array();
        
        // Add country tag
        $countries = get_the_terms($movie_id, 'movie_country');
        if ($countries && !is_wp_error($countries)) {
            foreach ($countries as $country) {
                $tags[] = '<a href="' . get_term_link($country) . '" class="movie-tag country-tag">' . $country->name . '</a>';
            }
        }

        // Add language tags
        $languages = get_post_meta($movie_id, 'languages', true);
        if ($languages) {
            $lang_array = explode(',', $languages);
            foreach ($lang_array as $lang) {
                $lang = trim($lang);
                if ($lang) {
                    $tags[] = '<span class="movie-tag language-tag">' . $lang . '</span>';
                }
            }
        }

        // Add special tags
        if (has_term('พากย์ไทย', 'movie_language', $movie_id)) {
            $tags[] = '<span class="movie-tag language-tag thai-dub">พากย์ไทย</span>';
        }
        if (has_term('ซับไทย', 'movie_language', $movie_id)) {
            $tags[] = '<span class="movie-tag language-tag thai-sub">ซับไทย</span>';
        }

        if (!empty($tags)) :
        ?>
            <div class="movie-tags">
                <?php echo implode(' ', $tags); ?>
            </div>
        <?php endif; ?>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <a href="<?php the_permalink(); ?>" class="btn btn-primary btn-sm">
                <?php _e('ดูหนัง', 'doomovie-theme'); ?>
            </a>
        </div>

    </div>

    <!-- Loading State -->
    <div class="movie-card-loading" style="display: none;">
        <div class="loading-spinner">
            <img src="<?php echo DOOMOVIE_THEME_URL; ?>/assets/images/loading.gif" alt="Loading...">
        </div>
    </div>

</article>

<?php
// Increment view count if this is a single movie page
if (is_singular('movie') && $movie_id === get_queried_object_id()) {
    increment_movie_view_count($movie_id);
}
?>
