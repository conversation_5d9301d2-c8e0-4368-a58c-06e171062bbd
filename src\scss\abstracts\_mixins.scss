// Responsive Breakpoints
@mixin respond-to($breakpoint) {
  @if $breakpoint == xs {
    @media (min-width: 0) { @content; }
  }
  @if $breakpoint == sm {
    @media (min-width: 576px) { @content; }
  }
  @if $breakpoint == md {
    @media (min-width: 768px) { @content; }
  }
  @if $breakpoint == lg {
    @media (min-width: 992px) { @content; }
  }
  @if $breakpoint == xl {
    @media (min-width: 1200px) { @content; }
  }
  @if $breakpoint == 2xl {
    @media (min-width: 1400px) { @content; }
  }
}

// Container
@mixin container($max-width: var(--container-xl)) {
  width: 100%;
  max-width: $max-width;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

// Flexbox utilities
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

// Grid utilities
@mixin grid($columns: auto-fit, $min-width: 250px, $gap: var(--grid-gap)) {
  display: grid;
  grid-template-columns: repeat($columns, minmax($min-width, 1fr));
  gap: $gap;
}

// Aspect ratio
@mixin aspect-ratio($width, $height) {
  aspect-ratio: $width / $height;
  
  // Fallback for older browsers
  @supports not (aspect-ratio: 1) {
    position: relative;
    
    &::before {
      content: '';
      display: block;
      padding-top: percentage($height / $width);
    }
    
    > * {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }
}

// Text utilities
@mixin text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin text-clamp($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// Button styles
@mixin button-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--border-radius-md);
  font-family: inherit;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  user-select: none;
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

@mixin button-primary {
  @include button-base;
  background-color: var(--color-primary);
  color: var(--color-text-primary);
  
  &:hover:not(:disabled) {
    background-color: var(--color-primary-light);
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
  }
}

@mixin button-secondary {
  @include button-base;
  background-color: transparent;
  color: var(--color-text-secondary);
  border: 1px solid var(--color-border);
  
  &:hover:not(:disabled) {
    background-color: var(--color-bg-tertiary);
    color: var(--color-text-primary);
  }
}

// Card styles
@mixin card {
  background-color: var(--color-bg-card);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  transition: transform var(--transition-normal);
  
  &:hover {
    transform: translateY(-4px);
  }
}

// Overlay
@mixin overlay($opacity: 0.8) {
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, $opacity);
    z-index: 1;
  }
}

// Loading animation
@mixin loading-skeleton {
  background: linear-gradient(
    90deg,
    var(--color-bg-tertiary) 25%,
    var(--color-bg-card) 50%,
    var(--color-bg-tertiary) 75%
  );
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// Scrollbar
@mixin custom-scrollbar {
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: var(--color-bg-secondary);
  }
  
  &::-webkit-scrollbar-thumb {
    background: var(--color-border);
    border-radius: var(--border-radius-full);
    
    &:hover {
      background: var(--color-border-light);
    }
  }
}

// Focus styles
@mixin focus-ring {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

// Visually hidden
@mixin visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

// Image cover
@mixin image-cover {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

// Backdrop blur
@mixin backdrop-blur($blur: 10px) {
  backdrop-filter: blur($blur);
  -webkit-backdrop-filter: blur($blur);
}

// Movie card hover effect
@mixin movie-card-hover {
  transition: all var(--transition-normal);
  
  &:hover {
    transform: scale(1.05);
    z-index: 10;
    
    .movie-card__overlay {
      opacity: 1;
    }
    
    .movie-card__info {
      transform: translateY(0);
    }
  }
}
